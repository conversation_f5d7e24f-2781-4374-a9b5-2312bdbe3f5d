<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JJ Code AI Builder</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles */
        :root {
            --light-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --light-container-bg: #FFFFFF;
            --light-text-primary: #1F2937;
            --light-accent: #4F46E5;
            --light-border: #E5E7EB;
            --light-shadow: rgba(0, 0, 0, 0.1);
        }

        html[data-theme='dark'] {
            --dark-bg: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            --dark-container-bg: #2D3748;
            --dark-text-primary: #F7FAFC;
            --dark-accent: #667EEA;
            --dark-border: #4A5568;
            --dark-shadow: rgba(0, 0, 0, 0.4);
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--dark-bg);
            color: var(--dark-text-primary);
            transition: background 0.3s ease, color 0.3s ease;
        }

        html[data-theme='light'] body {
            background: var(--light-bg);
            color: var(--light-text-primary);
        }

        /* Utility classes for smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* Enhanced Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem 2rem;
            background: linear-gradient(135deg, var(--dark-container-bg) 0%, rgba(45, 55, 72, 0.95) 100%);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid var(--dark-border);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        html[data-theme='light'] header {
            background: linear-gradient(135deg, var(--light-container-bg) 0%, rgba(255, 255, 255, 0.95) 100%);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            border-bottom-color: var(--light-border);
        }

        .app-title {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--dark-text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
        }

        html[data-theme='light'] .app-title {
            color: var(--light-text-primary);
        }

        .app-title svg {
            width: 2rem;
            height: 2rem;
            fill: var(--dark-accent);
            transition: transform 0.3s ease;
        }

        .app-title:hover svg {
            transform: scale(1.1) rotate(5deg);
        }

        html[data-theme='light'] .app-title svg {
            fill: var(--light-accent);
        }

        .theme-switcher-button {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            cursor: pointer;
            padding: 0.75rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        html[data-theme='light'] .theme-switcher-button {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
            border-color: rgba(79, 70, 229, 0.2);
            color: var(--light-text-primary);
            box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
        }

        .theme-switcher-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .theme-switcher-button:hover::before {
            left: 100%;
        }

        .theme-switcher-button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.3);
        }

        html[data-theme='light'] .theme-switcher-button:hover {
            box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);
            border-color: rgba(79, 70, 229, 0.3);
        }

        .theme-switcher-button:active {
            transform: translateY(0) scale(0.95);
            transition: all 0.1s ease;
        }

        .theme-switcher-button svg {
            width: 1.5rem;
            height: 1.5rem;
            fill: currentColor;
            transition: transform 0.3s ease;
        }

        .theme-switcher-button:hover svg {
            transform: rotate(15deg);
        }

        /* Header Navigation Buttons */
        .header-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-button {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            cursor: pointer;
            padding: 0.75rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
            min-width: 44px;
            min-height: 44px;
        }

        html[data-theme='light'] .nav-button {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
            border-color: rgba(79, 70, 229, 0.2);
            color: var(--light-text-primary);
            box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
        }

        .nav-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
            transition: left 0.5s ease;
        }

        .nav-button:hover::before {
            left: 100%;
        }

        .nav-button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.3);
        }

        html[data-theme='light'] .nav-button:hover {
            box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);
            border-color: rgba(79, 70, 229, 0.3);
        }

        .nav-button:active {
            transform: translateY(0) scale(0.95);
            transition: all 0.1s ease;
        }

        .nav-button svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: currentColor;
            transition: transform 0.3s ease;
        }

        .nav-button:hover svg {
            transform: scale(1.1);
        }

        .nav-button.settings-button:hover svg {
            transform: rotate(90deg) scale(1.1);
        }

        /* Settings Panel */
        .settings-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: linear-gradient(135deg, var(--dark-container-bg) 0%, rgba(45, 55, 72, 0.98) 100%);
            backdrop-filter: blur(20px);
            border-left: 1px solid var(--dark-border);
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.2);
            z-index: 2000;
            transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            padding: 2rem;
        }

        html[data-theme='light'] .settings-panel {
            background: linear-gradient(135deg, var(--light-container-bg) 0%, rgba(255, 255, 255, 0.98) 100%);
            border-left-color: var(--light-border);
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .settings-panel.open {
            right: 0;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--dark-border);
        }

        html[data-theme='light'] .settings-header {
            border-bottom-color: var(--light-border);
        }

        .settings-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .settings-title {
            color: var(--light-text-primary);
        }

        .settings-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            color: var(--dark-text-primary);
            transition: all 0.3s ease;
        }

        .settings-close:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: scale(1.1);
        }

        html[data-theme='light'] .settings-close {
            color: var(--light-text-primary);
        }

        html[data-theme='light'] .settings-close:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .settings-section {
            margin-bottom: 2rem;
        }

        .settings-section h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .settings-section h3 {
            color: var(--light-text-primary);
        }

        .settings-input-group {
            margin-bottom: 1.5rem;
        }

        .settings-input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .settings-input-group label {
            color: var(--light-text-primary);
        }

        .settings-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            background-color: rgba(45, 55, 72, 0.6);
            color: var(--dark-text-primary);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        html[data-theme='light'] .settings-input {
            border-color: var(--light-border);
            background-color: var(--light-container-bg);
            color: var(--light-text-primary);
        }

        .settings-input:focus {
            outline: none;
            border-color: var(--dark-accent);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        html[data-theme='light'] .settings-input:focus {
            border-color: var(--light-accent);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .settings-button {
            background: linear-gradient(135deg, var(--dark-accent) 0%, #764BA2 100%);
            color: #FFFFFF;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
        }

        html[data-theme='light'] .settings-button {
            background: linear-gradient(135deg, var(--light-accent) 0%, #7C3AED 100%);
        }

        .settings-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        html[data-theme='light'] .settings-button:hover {
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .settings-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        /* Settings Actions */
        .settings-actions {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--dark-border);
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        html[data-theme='light'] .settings-actions {
            border-top-color: var(--light-border);
        }

        .save-settings-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: #FFFFFF;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            position: relative;
            overflow: hidden;
            flex: 1;
            min-width: 150px;
            min-height: 48px;
        }

        .save-settings-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .save-settings-button:hover::before {
            left: 100%;
        }

        .save-settings-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .save-settings-button:active {
            transform: translateY(-1px) scale(0.98);
            transition: all 0.1s ease;
        }

        .save-settings-button svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .save-settings-button:hover svg {
            transform: scale(1.15);
        }

        .save-settings-button.success {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
        }

        .save-settings-button.success:hover {
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.5);
        }

        .save-settings-button.loading {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            cursor: not-allowed;
        }

        .save-settings-button.loading svg {
            animation: spin 1s linear infinite;
        }

        .reset-settings-button {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: #FFFFFF;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            position: relative;
            overflow: hidden;
            flex: 1;
            min-width: 150px;
            min-height: 48px;
        }

        .reset-settings-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .reset-settings-button:hover::before {
            left: 100%;
        }

        .reset-settings-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        .reset-settings-button:active {
            transform: translateY(-1px) scale(0.98);
            transition: all 0.1s ease;
        }

        .reset-settings-button svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .reset-settings-button:hover svg {
            transform: scale(1.15) rotate(180deg);
        }

        .reset-settings-button.loading {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            cursor: not-allowed;
        }

        .reset-settings-button.loading svg {
            animation: spin 1s linear infinite;
        }

        /* App Container */
        .app-container {
            flex-grow: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Hero Section */
        .hero-section {
            text-align: center;
            padding: 4rem 0 3rem 0;
        }

        .hero-section h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            color: var(--dark-text-primary);
            background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        html[data-theme='light'] .hero-section h1 {
            color: var(--light-text-primary);
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-section p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 2rem auto;
            color: #A0AEC0;
            line-height: 1.6;
        }

        html[data-theme='light'] .hero-section p {
            color: #6B7280;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .prompt-area {
            background-color: var(--dark-container-bg);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 8px 32px var(--dark-shadow);
            border: 1px solid var(--dark-border);
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            backdrop-filter: blur(10px);
        }

        html[data-theme='light'] .prompt-area {
            background-color: var(--light-container-bg);
            box-shadow: 0 8px 32px var(--light-shadow);
            border-color: var(--light-border);
        }

        .prompt-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }

        .prompt-actions {
            display: flex;
            justify-content: center;
        }

        /* New Prompt Button */
        .new-prompt-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: #FFFFFF;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
            min-width: 120px;
            min-height: 44px;
        }

        .new-prompt-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .new-prompt-button:hover::before {
            left: 100%;
        }

        .new-prompt-button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .new-prompt-button:active {
            transform: translateY(0) scale(0.98);
            transition: all 0.1s ease;
        }

        .new-prompt-button svg {
            width: 1.125rem;
            height: 1.125rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .new-prompt-button:hover svg {
            transform: scale(1.15) rotate(90deg);
        }

        /* Loading state for new prompt button */
        .new-prompt-button.loading {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            cursor: not-allowed;
        }

        .new-prompt-button.loading svg {
            animation: spin 1s linear infinite;
        }

        .prompt-label {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--dark-text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        html[data-theme='light'] .prompt-label {
            color: var(--light-text-primary);
        }

        .prompt-label svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: var(--dark-accent);
        }

        html[data-theme='light'] .prompt-label svg {
            fill: var(--light-accent);
        }

        #prompt-input {
            width: 100%;
            padding: 1.25rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.75rem;
            font-size: 1rem;
            resize: vertical;
            min-height: 120px;
            background-color: rgba(45, 55, 72, 0.6);
            color: var(--dark-text-primary);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        html[data-theme='light'] #prompt-input {
            background-color: var(--light-container-bg);
            border-color: var(--light-border);
            color: var(--light-text-primary);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        #prompt-input::placeholder {
            color: #9CA3AF;
        }

        #prompt-input:focus {
            outline: none;
            border-color: var(--dark-accent);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        html[data-theme='light'] #prompt-input:focus {
            border-color: var(--light-accent);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        /* Enhanced Button Design System */
        .generate-button {
            background: linear-gradient(135deg, var(--dark-accent) 0%, #764BA2 100%);
            color: #FFFFFF;
            padding: 1.25rem 2.5rem;
            border: none;
            border-radius: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            align-self: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            min-width: 200px;
        }

        .generate-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .generate-button:hover::before {
            left: 100%;
        }

        html[data-theme='light'] .generate-button {
            background: linear-gradient(135deg, var(--light-accent) 0%, #7C3AED 100%);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        .generate-button:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        html[data-theme='light'] .generate-button:hover {
            box-shadow: 0 12px 35px rgba(79, 70, 229, 0.5);
        }

        .generate-button:active {
            transform: translateY(-2px) scale(0.98);
            transition: all 0.1s ease;
        }

        .generate-button:disabled {
            background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
            opacity: 0.6;
        }

        html[data-theme='light'] .generate-button:disabled {
            background: linear-gradient(135deg, #9CA3AF 0%, #6B7280 100%);
        }

        .generate-button svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .generate-button:hover svg {
            transform: translateX(2px);
        }

        /* Loading state for generate button */
        .generate-button.loading {
            background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
            cursor: not-allowed;
        }

        html[data-theme='light'] .generate-button.loading {
            background: linear-gradient(135deg, #9CA3AF 0%, #6B7280 100%);
        }

        .generate-button.loading svg {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Button ripple effect */
        .button-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Success state for copy button */
        .copy-button.success {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
        }

        .copy-button.success:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4) !important;
        }

        .code-preview-area {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        @media (min-width: 768px) {
            .code-preview-area {
                flex-direction: row;
            }
        }

        .code-container {
            flex: 1;
            background-color: var(--light-container-bg);
            border-radius: 0.75rem;
            box-shadow: 0 4px 12px var(--light-shadow);
            border: 1px solid var(--light-border);
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Ensures rounded corners clip content */
        }

        html[data-theme='dark'] .code-container {
            background-color: var(--dark-container-bg);
            box-shadow: 0 4px 12px var(--dark-shadow);
            border-color: var(--dark-border);
        }

        .code-container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--light-border);
            background-color: var(--light-container-bg);
        }

        html[data-theme='dark'] .code-container-header {
            border-bottom-color: var(--dark-border);
            background-color: var(--dark-container-bg);
        }

        .code-container-header .title {
            font-weight: 600;
            color: var(--light-text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        html[data-theme='dark'] .code-container-header .title {
            color: var(--dark-text-primary);
        }

        .code-container-header .title svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: var(--light-accent);
        }

        /* Enhanced Code Editor Action Buttons */
        .code-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .code-actions button {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            color: #FFFFFF;
            padding: 0.5rem 0.75rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
            position: relative;
            overflow: hidden;
            min-width: 80px;
            min-height: 32px;
        }

        .code-actions button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .code-actions button:hover::before {
            left: 100%;
        }

        .code-actions button.copy-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        html[data-theme='dark'] .code-actions button.copy-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .code-actions button.clear-button {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .code-actions button:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
        }

        .code-actions button.copy-button:hover {
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
        }

        .code-actions button.clear-button:hover {
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
        }

        .code-actions button:active {
            transform: translateY(-1px) scale(0.98);
            transition: all 0.1s ease;
        }

        .code-actions button:disabled {
            background: linear-gradient(135deg, #9CA3AF 0%, #6B7280 100%);
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
            opacity: 0.6;
        }

        html[data-theme='dark'] .code-actions button:disabled {
            background: linear-gradient(135deg, #4B5563 0%, #374151 100%);
        }

        .code-actions button svg {
            width: 0.875rem;
            height: 0.875rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .code-actions button:hover svg {
            transform: scale(1.1);
        }

        .code-actions button.copy-button:hover svg {
            transform: scale(1.1) rotate(3deg);
        }

        .code-actions button.clear-button:hover svg {
            transform: scale(1.1) rotate(-3deg);
        }

        .code-actions button.edit-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        html[data-theme='dark'] .code-actions button.edit-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .code-actions button.edit-button:hover {
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
        }

        .code-actions button.edit-button:hover svg {
            transform: scale(1.1) rotate(3deg);
        }

        .code-actions button.select-edit-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        html[data-theme='dark'] .code-actions button.select-edit-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .code-actions button.select-edit-button:hover {
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
        }

        .code-actions button.select-edit-button:hover svg {
            transform: scale(1.1) rotate(-3deg);
        }

        .code-actions button.ai-prompt-button {
            background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        html[data-theme='dark'] .code-actions button.ai-prompt-button {
            background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .code-actions button.ai-prompt-button:hover {
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.35);
        }

        .code-actions button.ai-prompt-button:hover svg {
            transform: scale(1.1) rotate(5deg);
        }

        /* Success state for copy button */
        .code-actions button.copy-button.success {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
        }

        .code-actions button.copy-button.success:hover {
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
        }

        /* Loading state for buttons */
        .code-actions button.loading {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            cursor: not-allowed;
        }

        .code-actions button.loading svg {
            animation: spin 1s linear infinite;
        }

        /* Notification System */
        /* Download button styling to match theme */
        .code-actions button.download-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: #FFFFFF;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        .code-actions button.download-button:hover {
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
        }
        html[data-theme='light'] .code-actions button.download-button {
            background: linear-gradient(135deg, #10B981 0%, #0EA5E9 100%);
        }

        /* Import button styling */
        .code-actions button.import-button {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: #FFFFFF;
            box-shadow: 0 2px 8px rgba(79, 70, 229, 0.25);
        }
        .code-actions button.import-button:hover {
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.35);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--dark-container-bg) 0%, rgba(45, 55, 72, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid var(--dark-border);
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 350px;
        }

        html[data-theme='light'] .notification {
            background: linear-gradient(135deg, var(--light-container-bg) 0%, rgba(255, 255, 255, 0.95) 100%);
            border-color: var(--light-border);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .notification-content {
            color: var(--light-text-primary);
        }

        .notification-icon {
            width: 1.25rem;
            height: 1.25rem;
            fill: currentColor;
            flex-shrink: 0;
        }

        .notification-success .notification-icon {
            fill: #10B981;
        }

        .notification-error .notification-icon {
            fill: #EF4444;
        }

        .notification-info .notification-icon {
            fill: #3B82F6;
        }

        .notification span {
            font-size: 0.9rem;
            font-weight: 500;
            line-height: 1.4;
        }

        .code-editor-content {
            flex-grow: 1;
            padding: 1.5rem;
            overflow-y: auto;
            font-family: 'Fira Code', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap; /* Preserve whitespace and wrap lines */
            word-break: break-all; /* Break words to prevent overflow */
            color: var(--light-text-primary);
        }

        html[data-theme='dark'] .code-editor-content {
            color: var(--dark-text-primary);
        }

        .live-preview-container {
            flex: 1;
            background-color: var(--light-container-bg);
            border-radius: 0.75rem;
            box-shadow: 0 4px 12px var(--light-shadow);
            border: 1px solid var(--light-border);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        html[data-theme='dark'] .live-preview-container {
            background-color: var(--dark-container-bg);
            box-shadow: 0 4px 12px var(--dark-shadow);
            border-color: var(--dark-border);
        }

        .live-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--light-border);
            background-color: var(--light-container-bg);
        }

        html[data-theme='dark'] .live-preview-header {
            border-bottom-color: var(--dark-border);
            background-color: var(--dark-container-bg);
        }

        .live-preview-header .title {
            font-weight: 600;
            color: var(--light-text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        html[data-theme='dark'] .live-preview-header .title {
            color: var(--dark-text-primary);
        }

        .live-preview-header .title svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: var(--light-accent);
        }

        .preview-actions button {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            color: #FFFFFF;
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 0.75rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
            position: relative;
            overflow: hidden;
            min-width: 100px;
        }

        .preview-actions button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
            transition: left 0.5s ease;
        }

        .preview-actions button:hover::before {
            left: 100%;
        }

        .preview-actions button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        .preview-actions button:active {
            transform: translateY(0) scale(0.95);
            transition: all 0.1s ease;
        }

        .preview-actions button svg {
            width: 1rem;
            height: 1rem;
            fill: #FFFFFF;
            transition: transform 0.3s ease;
        }

        .preview-actions button:hover svg {
            transform: scale(1.1);
        }

        .preview-iframe {
            flex-grow: 1;
            width: 100%;
            border: none;
            background-color: #F9FAFB; /* Light background for iframe content */
            transition: opacity 0.3s ease;
        }

        .preview-iframe.updating {
            opacity: 0.5;
            pointer-events: none;
        }

        html[data-theme='dark'] .preview-iframe {
            background-color: #111827; /* Dark background for iframe content */
        }

        .initial-preview-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #9CA3AF;
            font-size: 1.1rem;
            text-align: center;
            gap: 1rem;
        }

        .initial-preview-message svg {
            width: 3rem;
            height: 3rem;
            fill: #D1D5DB;
        }

        /* Features Section */
        .features-section {
            padding: 4rem 0;
            text-align: center;
        }

        .features-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .features-section h2 {
            color: var(--light-text-primary);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .feature-card {
            background-color: var(--dark-container-bg);
            padding: 2.5rem 2rem;
            border-radius: 1rem;
            box-shadow: 0 8px 32px var(--dark-shadow);
            border: 1px solid var(--dark-border);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        html[data-theme='light'] .feature-card {
            background-color: var(--light-container-bg);
            box-shadow: 0 8px 32px var(--light-shadow);
            border-color: var(--light-border);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px var(--dark-shadow);
        }

        html[data-theme='light'] .feature-card:hover {
            box-shadow: 0 12px 40px var(--light-shadow);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            margin: 0 auto 1.5rem auto;
            background: linear-gradient(135deg, var(--dark-accent) 0%, #764BA2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        html[data-theme='light'] .feature-icon {
            background: linear-gradient(135deg, var(--light-accent) 0%, #7C3AED 100%);
        }

        .feature-icon svg {
            width: 2rem;
            height: 2rem;
            fill: #FFFFFF;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .feature-card h3 {
            color: var(--light-text-primary);
        }

        .feature-card p {
            color: #A0AEC0;
            line-height: 1.6;
            font-size: 1rem;
        }

        html[data-theme='light'] .feature-card p {
            color: #6B7280;
        }

        /* Responsive adjustments */
        @media (max-width: 767px) {
            header {
                padding: 1rem;
            }

            .app-title {
                font-size: 1.2rem;
            }

            .hero-section h1 {
                font-size: 2.5rem;
            }

            .hero-section p {
                font-size: 0.9rem;
            }

            .app-container {
                padding: 1rem;
            }

            .prompt-area {
                padding: 1.5rem;
            }

            .prompt-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .new-prompt-button {
                width: 100%;
                justify-content: center;
            }

            .generate-button {
                width: 100%;
                padding: 0.8rem 1.5rem;
            }

            .code-container-header, .live-preview-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                padding: 1rem;
            }

            .code-actions, .preview-actions {
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }

            .code-actions button, .preview-actions button {
                margin-left: 0.25rem;
                margin-right: 0;
            }

            .features-section {
                padding: 2rem 0;
            }

            .features-section h2 {
                font-size: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card {
                padding: 2rem 1.5rem;
            }

            .settings-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .save-settings-button,
            .reset-settings-button {
                width: 100%;
                justify-content: center;
            }
        }

        /* Settings Panel Styles */
        .settings-panel {
            background-color: var(--light-container-bg);
            box-shadow: 0 4px 12px var(--light-shadow);
            border: 1px solid var(--light-border);
        }
        html[data-theme='dark'] .settings-panel {
            background-color: var(--dark-container-bg);
            box-shadow: 0 4px 12px var(--dark-shadow);
            border-color: var(--dark-border);
        }

        .slide-down {
            animation: slideDown 0.5s ease-out forwards;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .api-key-input {
            background-color: var(--light-container-bg);
            border: 1px solid var(--light-border);
            color: var(--light-text-primary);
        }
        html[data-theme='dark'] .api-key-input {
            background-color: #2D3748; /* Slightly lighter dark for input */
            border-color: var(--dark-border);
            color: var(--dark-text-primary);
        }

        .accent-text {
            color: var(--light-accent);
        }
        html[data-theme='dark'] .accent-text {
            color: var(--dark-accent);
        }

        /* Selective Edit Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1 !important;
            visibility: visible !important;
            display: flex !important;
        }

        .modal-content {
            background: var(--dark-container-bg);
            border-radius: 1rem;
            border: 1px solid var(--dark-border);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
            width: 1000px;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s ease;
        }

        html[data-theme='light'] .modal-content {
            background: var(--light-container-bg);
            border-color: var(--light-border);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .modal-overlay.show .modal-content {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--dark-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        html[data-theme='light'] .modal-header {
            border-bottom-color: var(--light-border);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .modal-header h3 {
            color: var(--light-text-primary);
        }

        .close-modal-btn {
            background: none;
            border: none;
            color: var(--dark-text-primary);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        html[data-theme='light'] .close-modal-btn {
            color: var(--light-text-primary);
        }

        .close-modal-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        html[data-theme='light'] .close-modal-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .close-modal-btn svg {
            width: 1.25rem;
            height: 1.25rem;
            fill: currentColor;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .selective-editor-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            height: 500px;
        }

        .editor-section, .edit-section {
            display: flex;
            flex-direction: column;
        }

        .editor-section label, .edit-section label {
            font-weight: 500;
            margin-bottom: 0.75rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .editor-section label,
        html[data-theme='light'] .edit-section label {
            color: var(--light-text-primary);
        }

        .selective-code-textarea, .edit-instructions-textarea {
            flex: 1;
            padding: 1rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            color: var(--dark-text-primary);
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
        }

        html[data-theme='light'] .selective-code-textarea,
        html[data-theme='light'] .edit-instructions-textarea {
            background: var(--light-container-bg);
            border-color: var(--light-border);
            color: var(--light-text-primary);
        }

        .selective-code-textarea:focus, .edit-instructions-textarea:focus {
            border-color: var(--dark-accent);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        html[data-theme='light'] .selective-code-textarea:focus,
        html[data-theme='light'] .edit-instructions-textarea:focus {
            border-color: var(--light-accent);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .selective-code-textarea::selection {
            background: rgba(102, 126, 234, 0.3);
        }

        .selection-info {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            color: var(--dark-accent);
        }

        html[data-theme='light'] .selection-info {
            background: rgba(79, 70, 229, 0.1);
            color: var(--light-accent);
        }

        .edit-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .apply-edit-btn, .cancel-edit-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .apply-edit-btn {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
        }

        .apply-edit-btn:hover:not(:disabled) {
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            transform: translateY(-1px);
        }

        .apply-edit-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .apply-edit-btn svg {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        .cancel-edit-btn {
            background: transparent;
            color: var(--dark-text-primary);
            border: 1px solid var(--dark-border);
        }

        html[data-theme='light'] .cancel-edit-btn {
            color: var(--light-text-primary);
            border-color: var(--light-border);
        }

        .cancel-edit-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        html[data-theme='light'] .cancel-edit-btn:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        /* AI Prompt Modal Specific Styles */
        .ai-prompt-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            height: 600px;
        }

        .prompt-section, .preview-section {
            display: flex;
            flex-direction: column;
        }

        .ai-prompt-textarea {
            flex: 1;
            padding: 1rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            color: var(--dark-text-primary);
            font-family: 'Inter', sans-serif;
            font-size: 0.875rem;
            line-height: 1.5;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        html[data-theme='light'] .ai-prompt-textarea {
            background: var(--light-container-bg);
            border-color: var(--light-border);
            color: var(--light-text-primary);
        }

        .ai-prompt-textarea:focus {
            border-color: #8B5CF6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        .prompt-suggestions {
            margin-top: 1rem;
        }

        .prompt-suggestions h4 {
            margin: 0 0 0.75rem 0;
            font-size: 0.875rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .prompt-suggestions h4 {
            color: var(--light-text-primary);
        }

        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.375rem;
            background: rgba(139, 92, 246, 0.1);
            color: #8B5CF6;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        html[data-theme='light'] .suggestion-btn {
            border-color: var(--light-border);
            background: rgba(139, 92, 246, 0.1);
        }

        .suggestion-btn:hover {
            background: rgba(139, 92, 246, 0.2);
            border-color: #8B5CF6;
        }

        .code-preview-box {
            flex: 1;
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            overflow: auto;
            margin-bottom: 1rem;
        }

        html[data-theme='light'] .code-preview-box {
            background: var(--light-container-bg);
            border-color: var(--light-border);
        }

        .code-preview-box pre {
            margin: 0;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.75rem;
            line-height: 1.4;
            color: var(--dark-text-primary);
            white-space: pre-wrap;
            word-break: break-word;
        }

        html[data-theme='light'] .code-preview-box pre {
            color: var(--light-text-primary);
        }

        .update-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .apply-update-btn, .cancel-update-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .apply-update-btn {
            background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
            color: white;
        }

        .apply-update-btn:hover:not(:disabled) {
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            transform: translateY(-1px);
        }

        .apply-update-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .apply-update-btn svg {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        .cancel-update-btn {
            background: transparent;
            color: var(--dark-text-primary);
            border: 1px solid var(--dark-border);
        }

        html[data-theme='light'] .cancel-update-btn {
            color: var(--light-text-primary);
            border-color: var(--light-border);
        }

        .cancel-update-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        html[data-theme='light'] .cancel-update-btn:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        @media (max-width: 1024px) {
            .selective-editor-container, .ai-prompt-container {
                grid-template-columns: 1fr;
                height: auto;
            }

            .modal-content {
                width: 95vw;
                max-height: 95vh;
            }
        }
    </style>
    <style>
        /* Import Modal Styles */
        .input-group { margin-bottom: 1em; }
        .input-group label { display: block; margin-bottom: .5em; }
        .input-group input[type="file"] { width: 100%; }

        /* Enhanced Select & Edit Modal Styles */
        .modal-header-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--dark-border);
        }

        html[data-theme='light'] .editor-header {
            border-bottom-color: var(--light-border);
        }

        .editor-tools {
            display: flex;
            gap: 0.5rem;
        }

        .tool-btn {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            color: var(--dark-text-primary);
            padding: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        html[data-theme='light'] .tool-btn {
            background: rgba(79, 70, 229, 0.1);
            border-color: rgba(79, 70, 229, 0.2);
            color: var(--light-text-primary);
        }

        .tool-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        .tool-btn.active {
            background: var(--dark-accent);
            color: white;
        }

        html[data-theme='light'] .tool-btn.active {
            background: var(--light-accent);
        }

        .tool-btn svg {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        .editor-container {
            position: relative;
            display: flex;
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            overflow: hidden;
            height: 300px;
        }

        html[data-theme='light'] .editor-container {
            border-color: var(--light-border);
        }

        .line-numbers {
            background: rgba(0, 0, 0, 0.1);
            color: #888;
            padding: 1rem 0.5rem;
            font-family: 'Fira Code', monospace;
            font-size: 0.85rem;
            line-height: 1.5;
            text-align: right;
            user-select: none;
            border-right: 1px solid var(--dark-border);
            min-width: 3rem;
            overflow: hidden;
        }

        html[data-theme='light'] .line-numbers {
            background: rgba(0, 0, 0, 0.05);
            border-right-color: var(--light-border);
        }

        .selective-code-textarea {
            flex: 1;
            border: none;
            padding: 1rem;
            font-family: 'Fira Code', monospace;
            font-size: 0.85rem;
            line-height: 1.5;
            background: transparent;
            color: var(--dark-text-primary);
            resize: none;
            outline: none;
        }

        html[data-theme='light'] .selective-code-textarea {
            color: var(--light-text-primary);
        }

        .selection-details {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
            color: #888;
        }

        .edit-tabs {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--dark-border);
        }

        html[data-theme='light'] .edit-tabs {
            border-bottom-color: var(--light-border);
        }

        .tab-btn {
            background: transparent;
            border: none;
            padding: 0.75rem 1rem;
            cursor: pointer;
            color: #888;
            font-size: 0.9rem;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn:hover {
            color: var(--dark-text-primary);
            background: rgba(102, 126, 234, 0.1);
        }

        html[data-theme='light'] .tab-btn:hover {
            color: var(--light-text-primary);
            background: rgba(79, 70, 229, 0.1);
        }

        .tab-btn.active {
            color: var(--dark-accent);
            border-bottom-color: var(--dark-accent);
        }

        html[data-theme='light'] .tab-btn.active {
            color: var(--light-accent);
            border-bottom-color: var(--light-accent);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        .edit-mode-options {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 0.5rem;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        html[data-theme='light'] .edit-mode-options {
            background: rgba(79, 70, 229, 0.05);
            border-color: rgba(79, 70, 229, 0.1);
        }

        .radio-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            color: var(--dark-text-primary);
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            font-weight: 500;
        }

        html[data-theme='light'] .quick-action-btn {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
            border-color: rgba(79, 70, 229, 0.2);
            color: var(--light-text-primary);
        }

        .quick-action-btn:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .advanced-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .option-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .option-group label {
            font-weight: 500;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .option-group label {
            color: var(--light-text-primary);
        }

        .range-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #888;
        }

        .preview-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            height: 400px;
        }

        .preview-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .preview-section h4 {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .preview-section h4 {
            color: var(--light-text-primary);
        }

        .preview-code {
            flex: 1;
            background: rgba(0, 0, 0, 0.1);
            border: 1px solid var(--dark-border);
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'Fira Code', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
            overflow: auto;
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .preview-code {
            background: rgba(0, 0, 0, 0.05);
            border-color: var(--light-border);
            color: var(--light-text-primary);
        }

        .find-replace-panel {
            background: var(--dark-container-bg);
            border-top: 1px solid var(--dark-border);
            padding: 1rem;
        }

        html[data-theme='light'] .find-replace-panel {
            background: var(--light-container-bg);
            border-top-color: var(--light-border);
        }

        .find-replace-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .find-replace-row {
            display: flex;
            gap: 1rem;
        }

        .find-input, .replace-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid var(--dark-border);
            border-radius: 0.25rem;
            background: rgba(45, 55, 72, 0.6);
            color: var(--dark-text-primary);
        }

        html[data-theme='light'] .find-input,
        html[data-theme='light'] .replace-input {
            border-color: var(--light-border);
            background: var(--light-container-bg);
            color: var(--light-text-primary);
        }

        .find-replace-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .preview-edit-btn {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: #FFFFFF;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .preview-edit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .preview-edit-btn:disabled {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .preview-edit-btn svg {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>
    <div id="import-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Import Code</h3>
                <button id="close-import-modal" class="close-modal-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label for="separate-files-modal">Import separate index.html, styles.css, and script.js files:</label>
                    <input type="file" id="html-file-modal" accept=".html" placeholder="index.html">
                    <input type="file" id="css-file-modal" accept=".css" placeholder="styles.css">
                    <input type="file" id="js-file-modal" accept=".js" placeholder="script.js">
                </div>
                <div class="input-group">
                    <label for="combined-file-modal">Import a single combined HTML file:</label>
                    <input type="file" id="combined-file-modal" accept=".html">
                </div>
                <div class="input-group">
                    <label for="zip-file-modal">Import from a ZIP file:</label>
                    <input type="file" id="zip-file-modal" accept=".zip">
                </div>
                <div class="input-group">
                    <label for="folder-input-modal">Import from a folder:</label>
                    <input type="file" id="folder-input-modal" webkitdirectory directory>
                </div>
                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <button id="process-files-modal" class="settings-button">Process Files</button>
                    <button id="clear-import-files" class="reset-settings-button">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                        </svg>
                        Clear All
                    </button>
                </div>
            </div>
        </div>
    </div>
    <header class="transition-all">
        <div class="app-title transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z"/>
            </svg>
            JJ Code AI Builder
        </div>
        <div class="header-nav">
            <button id="theme-switcher" class="nav-button theme-switcher-button transition-all">
                <!-- Sun icon for light mode -->
                <svg id="sun-icon" class="hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 2.5C12.8284 2.5 13.5 3.17157 13.5 4C13.5 4.82843 12.8284 5.5 12 5.5C11.1716 5.5 10.5 4.82843 10.5 4C10.5 3.17157 11.1716 2.5 12 2.5ZM12 18.5C11.1716 18.5 10.5 19.1716 10.5 20C10.5 20.8284 11.1716 21.5 12 21.5C12.8284 21.5 13.5 20.8284 13.5 20C13.5 19.1716 12.8284 18.5 12 18.5ZM19.0711 5.07107C19.6569 4.48528 20.5429 4.48528 21.1287 5.07107C21.7145 5.65685 21.7145 6.54284 21.1287 7.12863L19.0711 5.07107ZM2.87127 16.8713C2.28548 17.4571 2.28548 18.3431 2.87127 18.9289C3.45706 19.5147 4.34305 19.5147 4.92883 18.9289L2.87127 16.8713ZM21.1287 16.8713C20.5429 17.4571 19.6569 17.4571 19.0711 16.8713L16.9289 19.0711C16.3431 19.6569 15.4571 19.6569 14.8713 19.0711C14.2855 18.4853 14.2855 17.5993 14.8713 17.0135L16.9289 19.0711L21.1287 16.8713ZM5.07107 2.87127C5.65685 2.28548 6.54284 2.28548 7.12863 2.87127L5.07107 4.92883ZM18.9289 4.92883C19.5147 4.34305 19.5147 3.45706 18.9289 2.87127L16.8713 4.92883ZM4 10.5C3.17157 10.5 2.5 11.1716 2.5 12C2.5 12.8284 3.17157 13.5 4 13.5C4.82843 13.5 5.5 12.8284 5.5 12C5.5 11.1716 4.82843 10.5 4 10.5ZM20 10.5C19.1716 10.5 18.5 11.1716 18.5 12C18.5 12.8284 19.1716 13.5 20 13.5C20.8284 13.5 21.5 12.8284 21.5 12C21.5 11.1716 20.8284 10.5 20 10.5Z"/>
                </svg>
                <!-- Moon icon for dark mode -->
                <svg id="moon-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12.3 4.3C13.6 4.3 14.9 4.6 16 5.2C14.5 3.3 12.3 2 9.8 2C5.5 2 2 5.5 2 9.8C2 12.3 3.3 14.5 5.2 16C4.6 14.9 4.3 13.6 4.3 12.3C4.3 7.8 7.8 4.3 12.3 4.3ZM12.3 6.3C8.9 6.3 6.3 8.9 6.3 12.3C6.3 15.7 8.9 18.3 12.3 18.3C15.7 18.3 18.3 15.7 18.3 12.3C18.3 8.9 15.7 6.3 12.3 6.3ZM12.3 8.3C14.6 8.3 16.3 10.0 16.3 12.3C16.3 14.6 14.6 16.3 12.3 16.3C10.0 16.3 8.3 14.6 8.3 12.3C8.3 10.0 10.0 8.3 12.3 8.3Z"/>
                </svg>
            </button>
            <button id="settings-button" class="nav-button settings-button transition-all" title="Settings">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5ZM12 10.5C13.1046 10.5 14 11.3954 14 12.5C14 13.6046 13.1046 14.5 12 14.5C10.8954 14.5 10 13.6046 10 12.5C10 11.3954 10.8954 10.5 12 10.5Z"/>
                    <path d="M19.43 12.97C19.47 12.65 19.5 12.33 19.5 12C19.5 11.67 19.47 11.35 19.43 11.03L21.54 9.37C21.73 9.22 21.78 8.95 21.66 8.73L19.66 5.27C19.54 5.05 19.27 4.96 19.05 5.05L16.56 6.05C16.04 5.66 15.5 5.32 14.87 5.07L14.5 2.42C14.46 2.18 14.25 2 14 2H10C9.75 2 9.54 2.18 9.5 2.42L9.13 5.07C8.5 5.32 7.96 5.66 7.44 6.05L4.95 5.05C4.73 4.96 4.46 5.05 4.34 5.27L2.34 8.73C2.22 8.95 2.27 9.22 2.46 9.37L4.57 11.03C4.53 11.35 4.5 11.67 4.5 12C4.5 12.33 4.53 12.65 4.57 12.97L2.46 14.63C2.27 14.78 2.22 15.05 2.34 15.27L4.34 18.73C4.46 18.95 4.73 19.04 4.95 18.95L7.44 17.95C7.96 18.34 8.5 18.68 9.13 18.93L9.5 21.58C9.54 21.82 9.75 22 10 22H14C14.25 22 14.46 21.82 14.5 21.58L14.87 18.93C15.5 18.68 16.04 18.34 16.56 17.95L19.05 18.95C19.27 19.04 19.54 18.95 19.66 18.73L21.66 15.27C21.78 15.05 21.73 14.78 21.54 14.63L19.43 12.97Z"/>
                </svg>
            </button>
            <button id="help-button" class="nav-button transition-all" title="Help">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Settings Panel -->
    <div id="settings-overlay" class="settings-overlay"></div>
    <div id="settings-panel" class="settings-panel">
        <div class="settings-header">
            <h2 class="settings-title">Settings</h2>
            <button id="settings-close" class="settings-close">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                </svg>
            </button>
        </div>

        <div class="settings-section">
            <h3>API Configuration</h3>
            <div class="settings-input-group">
                <label for="api-key-input">Gemini API Key</label>
                <input type="password" id="api-key-input" class="settings-input" placeholder="Enter your Gemini API key">
                <div style="margin-top: 0.5rem;">
                    <button id="toggle-api-key" class="settings-button" style="margin-right: 0.5rem;">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="margin-right: 0.25rem;">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 11.5C2.73 15.39 7 18.5 12 18.5C17 18.5 21.27 15.39 23 11.5C21.27 7.61 17 4.5 12 4.5ZM12 16.5C9.24 16.5 7 14.26 7 11.5C7 8.74 9.24 6.5 12 6.5C14.76 6.5 17 8.74 17 11.5C17 14.26 14.76 16.5 12 16.5ZM12 8.5C10.34 8.5 9 9.84 9 11.5C9 13.16 10.34 14.5 12 14.5C13.66 14.5 15 13.16 15 11.5C15 9.84 13.66 8.5 12 8.5Z"/>
                        </svg>
                        Show
                    </button>
                    <button id="test-api-key" class="settings-button">
                        Test Key
                    </button>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h3>Appearance</h3>
            <div class="settings-input-group">
                <label>Theme</label>
                <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                    <button id="theme-dark" class="settings-button" style="flex: 1;">Dark</button>
                    <button id="theme-light" class="settings-button" style="flex: 1;">Light</button>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h3>Code Generation</h3>
            <div class="settings-input-group">
                <label for="model-select">AI Model</label>
                <select id="model-select" class="settings-input">
                    <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash</option>
                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                </select>
            </div>
            <div class="settings-input-group">
                <label for="temperature-input">Creativity Level</label>
                <input type="range" id="temperature-input" class="settings-input" min="0" max="1" step="0.1" value="0.7">
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin-top: 0.25rem;">
                    <span>Focused</span>
                    <span>Creative</span>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h3>About</h3>
            <p style="color: var(--dark-text-primary); font-size: 0.9rem; line-height: 1.5;">
                JJ Code AI Builder v1.0.0<br>
                Powered by Google Gemini AI<br>
                Built with modern web technologies
            </p>
        </div>

        <div class="settings-actions">
            <button id="save-settings-button" class="save-settings-button transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/>
                </svg>
                Save Settings
            </button>
            <button id="reset-settings-button" class="reset-settings-button transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.88 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
                </svg>
                Reset to Defaults
            </button>
        </div>
    </div>

    <main class="app-container">
        <section class="hero-section">
            <h1>Turn Your Ideas into Code, Instantly</h1>
            <p>Let AI write HTML, CSS, and JavaScript for you. The future of web development is here..</p>
        </section>

        <section class="main-content">
            <div class="prompt-area transition-all">
                <div class="prompt-header">
                    <label for="prompt-input" class="prompt-label transition-all">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M13.4 12L18 16.6L16.6 18L12 13.4L7.4 18L6 16.6L10.6 12L6 7.4L7.4 6L12 10.6L16.6 6L18 7.4L13.4 12Z"/>
                        </svg>
                        Describe your component
                    </label>
                    <button id="new-prompt-button" class="new-prompt-button transition-all" title="Start a new prompt">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                        </svg>
                        New Prompt
                    </button>
                </div>
                <textarea id="prompt-input" placeholder="Example: Create a responsive pricing card with three tiers, hover effects, and a gradient background..."></textarea>
                <div class="prompt-actions">
                    <button id="generate-button" class="generate-button transition-all">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M10 18L14 12L10 6L12 4L18 12L12 20L10 18Z"/>
                        </svg>
                        Generate Code
                    </button>
                </div>
            </div>

            <div class="code-preview-area">
                <div class="code-container transition-all">
                    <div class="code-container-header transition-all">
                        <div class="title transition-all">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z"/>
                            </svg>
                            Code Editor
                        </div>
                        <div class="code-actions">
                            <button id="copy-button" class="copy-button transition-all" disabled title="Copy code to clipboard">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM15 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H15C16.1 23 17 22.1 17 21V7C17 5.9 16.1 5 15 5ZM15 21H8V7H15V21Z"/>
                                </svg>
                                Copy
                            </button>
                            <!-- Edit button removed - Select & Edit provides the same functionality -->
                            <button id="select-edit-button" class="select-edit-button transition-all" title="Select and edit specific parts of code">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M9 7H7V9H9V7ZM9 11H7V13H9V11ZM9 15H7V17H9V15ZM13 15H11V17H13V15ZM13 11H11V13H13V11ZM13 7H11V9H13V7ZM17 15H15V17H17V15ZM17 11H15V13H17V11ZM17 7H15V9H17V7Z"/>
                                </svg>
                                Select & Edit
                            </button>
                            <button id="download-button" class="download-button transition-all" disabled title="Download current code as HTML">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M5 20h14v-2H5v2zM11 4h2v7h3l-4 4-4-4h3V4z"/>
                                </svg>
                                Download
                            </button>

                            <button id="import-code-btn" class="import-button transition-all" title="Import HTML/CSS/JS files or ZIP">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                                Import Code
                            </button>
                            <input type="file" id="import-file-input" multiple accept=".html,.css,.js,.zip" style="position: absolute; opacity: 0; pointer-events: none;">
                            <button id="clear-button" class="clear-button transition-all" title="Clear code editor">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                                </svg>
                                Clear
                            </button>
                        </div>
                    </div>
                    <pre id="code-editor" class="code-editor-content">// Your generated code will appear here...</pre>
                </div>

                <div class="live-preview-container transition-all">
                    <div class="live-preview-header transition-all">
                        <div class="title transition-all">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M12 4.5C7.5 4.5 3.73 7.15 2 11C3.73 14.85 7.5 17.5 12 17.5C16.5 17.5 20.27 14.85 22 11C20.27 7.15 16.5 4.5 12 4.5ZM12 15C9.24 15 7 12.76 7 10C7 7.24 9.24 5 12 5C14.76 5 17 7.24 17 10C17 12.76 14.76 15 12 15ZM12 7C10.34 7 9 8.34 9 10C9 11.66 10.34 13 12 13C13.66 13 15 11.66 15 10C15 8.34 13.66 7 12 7Z"/>
                            </svg>
                            Live Preview
                            <span id="preview-status" class="preview-status" style="display: none; margin-left: 0.5rem; font-size: 0.8rem; color: #888;">🔄 Updating...</span>
                        </div>
                        <div class="preview-actions">
                            <button id="refresh-button" class="transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.88 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
                                </svg>
                                Refresh
                            </button>
                            <button id="fullscreen-button" class="transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M7 14H5V19H10V17H7V14ZM5 10H7V7H10V5H5V10ZM14 17H17V14H19V19H14V17ZM17 7H14V5H19V10H17V7Z"/>
                                </svg>
                                Fullscreen
                            </button>
                        </div>
                    </div>
                    <iframe id="live-preview-iframe" class="preview-iframe">
                        <div class="initial-preview-message">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z"/>
                            </svg>
                            <p>Preview will appear here</p>
                        </div>
                    </iframe>
                </div>
            </div>
        </section>

        <section class="features-section">
            <h2>Why Choose JJ Code AI Builder?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M13 3L4 14H11L10.5 21L19.5 10H12.5L13 3Z"/>
                        </svg>
                    </div>
                    <h3>Lightning Fast</h3>
                    <p>Generate complete components in seconds with our advanced AI technology.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M17 7H22V9H19V12H17V9H14V7H17V4H19V7H22ZM4 6H12V8H4V6ZM4 11H12V13H4V11ZM4 16H8V18H4V16Z"/>
                        </svg>
                    </div>
                    <h3>Responsive Design</h3>
                    <p>All generated components are mobile-first and fully responsive.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>
                        </svg>
                    </div>
                    <h3>Beautiful UI</h3>
                    <p>Modern, clean designs that follow current web standards and trends.</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Enhanced Selective Edit Modal -->
    <div id="selective-edit-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content" style="max-width: 95vw; width: 1400px;">
            <div class="modal-header">
                <h3>🎯 Advanced Select & Edit</h3>
                <div class="modal-header-actions">
                    <button id="undo-edit" class="nav-button" title="Undo Last Edit" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M12.5 8C9.85 8 7.45 9 5.6 10.6L2 7V16H11L7.38 12.38C8.77 11.22 10.54 10.5 12.5 10.5C16.04 10.5 19.05 12.81 20.1 16L22.47 15.22C21.08 11.03 17.15 8 12.5 8Z"/>
                        </svg>
                    </button>
                    <button id="redo-edit" class="nav-button" title="Redo Last Edit" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M18.4 10.6C16.55 9 14.15 8 11.5 8C6.85 8 2.92 11.03 1.53 15.22L3.9 16C4.95 12.81 7.96 10.5 11.5 10.5C13.46 10.5 15.23 11.22 16.62 12.38L13 16H22V7L18.4 10.6Z"/>
                        </svg>
                    </button>
                    <button id="close-selective-modal" class="close-modal-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="selective-editor-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; height: 70vh;">
                    <!-- Left Panel: Code Editor -->
                    <div class="editor-section">
                        <div class="editor-header">
                            <label for="selective-code-editor">📝 Code Editor</label>
                            <div class="editor-tools">
                                <button id="select-all-code" class="tool-btn" title="Select All">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M3 5H1V3C1 2.45 1.45 2 2 2H4V4H3V5ZM3 13H1V11H3V13ZM3 9H1V7H3V9ZM3 17H1V15H3V17ZM3 21H1V19C1 18.45 1.45 18 2 18H4V20H3V21ZM21 3V1H19V3H21ZM21 7H23V5H21V7ZM21 11H23V9H21V11ZM21 15H23V13H21V15ZM21 19V21H19V23H21C21.55 23 22 22.55 22 22V20H21V19ZM15 21H17V23H15V21ZM11 21H13V23H11V21ZM7 21H9V23H7V21ZM15 1H17V3H15V1ZM11 1H13V3H11V1ZM7 1H9V3H7V1Z"/>
                                    </svg>
                                </button>
                                <button id="find-replace-btn" class="tool-btn" title="Find & Replace">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3S3 5.91 3 9.5S5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5S14 7.01 14 9.5S11.99 14 9.5 14Z"/>
                                    </svg>
                                </button>
                                <button id="line-numbers-toggle" class="tool-btn active" title="Toggle Line Numbers">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M2 17H4V19H2V17ZM2 7H4V9H2V7ZM6 7H22V9H6V7ZM2 12H4V14H2V12ZM6 12H22V14H6V12ZM6 17H22V19H6V17Z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="editor-container">
                            <div id="line-numbers" class="line-numbers"></div>
                            <textarea id="selective-code-editor" class="selective-code-textarea"></textarea>
                        </div>
                        <div class="selection-info" id="selection-info" style="display: none;">
                            <span id="selection-text">No text selected</span>
                            <div class="selection-details">
                                <span id="selection-lines">Lines: 0</span>
                                <span id="selection-chars">Chars: 0</span>
                                <span id="selection-position">Position: 0-0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel: Edit Options -->
                    <div class="edit-section">
                        <div class="edit-tabs">
                            <button class="tab-btn active" data-tab="instructions">✏️ Instructions</button>
                            <button class="tab-btn" data-tab="quick-actions">⚡ Quick Actions</button>
                            <button class="tab-btn" data-tab="advanced">🔧 Advanced</button>
                            <button class="tab-btn" data-tab="preview">👁️ Preview</button>
                        </div>

                        <!-- Instructions Tab -->
                        <div id="instructions-tab" class="tab-content active">
                            <label for="edit-instructions">Describe your changes:</label>
                            <textarea id="edit-instructions" class="edit-instructions-textarea" placeholder="Describe the changes you want to make...&#10;&#10;Examples:&#10;• Change the background color to blue&#10;• Add error handling&#10;• Make this function async&#10;• Add comments to explain the logic&#10;• Convert to arrow function&#10;• Add CSS animations"></textarea>

                            <div class="edit-mode-options">
                                <label>Edit Mode:</label>
                                <div class="radio-group">
                                    <label><input type="radio" name="edit-mode" value="replace" checked> Replace Selection</label>
                                    <label><input type="radio" name="edit-mode" value="insert-before"> Insert Before</label>
                                    <label><input type="radio" name="edit-mode" value="insert-after"> Insert After</label>
                                    <label><input type="radio" name="edit-mode" value="wrap"> Wrap Selection</label>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions Tab -->
                        <div id="quick-actions-tab" class="tab-content">
                            <div class="quick-actions-grid">
                                <button class="quick-action-btn" data-action="add-comments">💬 Add Comments</button>
                                <button class="quick-action-btn" data-action="remove-comments">🚫 Remove Comments</button>
                                <button class="quick-action-btn" data-action="format-code">🎨 Format Code</button>
                                <button class="quick-action-btn" data-action="minify-code">📦 Minify Code</button>
                                <button class="quick-action-btn" data-action="add-error-handling">🛡️ Add Error Handling</button>
                                <button class="quick-action-btn" data-action="make-async">⚡ Make Async</button>
                                <button class="quick-action-btn" data-action="add-logging">📊 Add Logging</button>
                                <button class="quick-action-btn" data-action="optimize-performance">🚀 Optimize</button>
                            </div>
                        </div>

                        <!-- Advanced Tab -->
                        <div id="advanced-tab" class="tab-content">
                            <div class="advanced-options">
                                <div class="option-group">
                                    <label>AI Model:</label>
                                    <select id="edit-model-select" class="settings-input">
                                        <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash (Fast)</option>
                                        <option value="gemini-1.5-pro">Gemini 1.5 Pro (Detailed)</option>
                                    </select>
                                </div>
                                <div class="option-group">
                                    <label>Creativity Level:</label>
                                    <input type="range" id="edit-temperature" min="0" max="1" step="0.1" value="0.3">
                                    <div class="range-labels">
                                        <span>Conservative</span>
                                        <span>Creative</span>
                                    </div>
                                </div>
                                <div class="option-group">
                                    <label>
                                        <input type="checkbox" id="preserve-formatting" checked>
                                        Preserve Original Formatting
                                    </label>
                                </div>
                                <div class="option-group">
                                    <label>
                                        <input type="checkbox" id="add-explanations">
                                        Include Explanatory Comments
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Tab -->
                        <div id="preview-tab" class="tab-content">
                            <div class="preview-container">
                                <div class="preview-section">
                                    <h4>Before:</h4>
                                    <pre id="preview-before" class="preview-code"></pre>
                                </div>
                                <div class="preview-section">
                                    <h4>After (Preview):</h4>
                                    <pre id="preview-after" class="preview-code"></pre>
                                </div>
                            </div>
                        </div>

                        <div class="edit-actions">
                            <button id="apply-selective-edit" class="apply-edit-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/>
                                </svg>
                                Apply Changes
                            </button>
                            <button id="preview-changes" class="preview-edit-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M12 4.5C7.5 4.5 3.73 7.15 2 11C3.73 14.85 7.5 17.5 12 17.5C16.5 17.5 20.27 14.85 22 11C20.27 7.15 16.5 4.5 12 4.5ZM12 15C9.24 15 7 12.76 7 10C7 7.24 9.24 5 12 5C14.76 5 17 7.24 17 10C17 12.76 14.76 15 12 15ZM12 7C10.34 7 9 8.34 9 10C9 11.66 10.34 13 12 13C13.66 13 15 11.66 15 10C15 8.34 13.66 7 12 7Z"/>
                                </svg>
                                Preview Changes
                            </button>
                            <button id="force-refresh-preview" class="tool-btn" title="Force Refresh Preview">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
                                </svg>
                            </button>
                            <button id="cancel-selective-edit" class="cancel-edit-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Find & Replace Panel -->
                <div id="find-replace-panel" class="find-replace-panel" style="display: none;">
                    <div class="find-replace-content">
                        <div class="find-replace-row">
                            <input type="text" id="find-input" placeholder="Find..." class="find-input">
                            <input type="text" id="replace-input" placeholder="Replace with..." class="replace-input">
                        </div>
                        <div class="find-replace-actions">
                            <button id="find-next" class="tool-btn">Next</button>
                            <button id="find-prev" class="tool-btn">Previous</button>
                            <button id="replace-one" class="tool-btn">Replace</button>
                            <button id="replace-all" class="tool-btn">Replace All</button>
                            <button id="close-find-replace" class="tool-btn">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Prompt Modal -->
    <div id="ai-prompt-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>AI Code Update</h3>
                <button id="close-ai-prompt-modal" class="close-modal-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="ai-prompt-container">
                    <div class="prompt-section">
                        <label for="ai-update-prompt">Describe how you want to update your code:</label>
                        <textarea id="ai-update-prompt" class="ai-prompt-textarea" placeholder="Enter your instructions for updating the code...

Examples:
• Add a dark mode toggle button
• Make the design responsive for mobile
• Add form validation with error messages
• Change the color scheme to blue and green
• Add smooth animations to buttons
• Include a navigation menu
• Add a footer with contact information
• Make the layout more modern and clean"></textarea>
                        <div class="prompt-suggestions">
                            <h4>Quick Suggestions:</h4>
                            <div class="suggestion-buttons">
                                <button class="suggestion-btn" data-prompt="Add a dark mode toggle">Dark Mode</button>
                                <button class="suggestion-btn" data-prompt="Make the design responsive for mobile devices">Responsive Design</button>
                                <button class="suggestion-btn" data-prompt="Add smooth animations and transitions">Add Animations</button>
                                <button class="suggestion-btn" data-prompt="Improve the color scheme and make it more modern">Modern Colors</button>
                                <button class="suggestion-btn" data-prompt="Add form validation with error messages">Form Validation</button>
                                <button class="suggestion-btn" data-prompt="Add a navigation menu">Navigation Menu</button>
                            </div>
                        </div>
                    </div>
                    <div class="preview-section">
                        <label>Current Code Preview:</label>
                        <div class="code-preview-box">
                            <pre id="ai-code-preview"></pre>
                        </div>
                        <div class="update-actions">
                            <label style="display:flex;align-items:center;gap:8px;margin-bottom:8px;">
                                <input type="checkbox" id="ai-minimal-mode" checked>
                                <span>Apply changes minimally (in-place)</span>
                            </label>
                            <button id="apply-ai-update" class="apply-update-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>
                                </svg>
                                Update Code with AI
                            </button>
                            <button id="cancel-ai-update" class="cancel-update-btn">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const htmlElement = document.documentElement;
            const themeSwitcher = document.getElementById('theme-switcher');
            const sunIcon = document.getElementById('sun-icon');
            const moonIcon = document.getElementById('moon-icon');
            const settingsButton = document.getElementById('settings-button');
            const helpButton = document.getElementById('help-button');
            const settingsPanel = document.getElementById('settings-panel');
            const settingsOverlay = document.getElementById('settings-overlay');
            const settingsClose = document.getElementById('settings-close');
            const saveSettingsButton = document.getElementById('save-settings-button');
            const resetSettingsButton = document.getElementById('reset-settings-button');
            const promptInput = document.getElementById('prompt-input');
            const newPromptButton = document.getElementById('new-prompt-button');
            const generateButton = document.getElementById('generate-button');
            let codeEditor = document.getElementById('code-editor'); // changed to let to allow safe reassignment after manual edits
            const livePreviewIframe = document.getElementById('live-preview-iframe');
            const copyButton = document.getElementById('copy-button');
            // editButton removed - Select & Edit provides the same functionality
            const downloadButton = document.getElementById('download-button');
            const selectEditButton = document.getElementById('select-edit-button');
            const importButton = document.getElementById('import-code-btn');
            const importFileInput = document.getElementById('import-file-input');
            const aiPromptButton = document.getElementById('ai-prompt-button');
            const importModal = document.getElementById('import-modal');
            const closeImportModal = document.getElementById('close-import-modal');
            const processFilesModal = document.getElementById('process-files-modal');
            const clearButton = document.getElementById('clear-button');
            const refreshButton = document.getElementById('refresh-button');
            const fullscreenButton = document.getElementById('fullscreen-button');

            // Selective edit modal elements
            const selectiveEditModal = document.getElementById('selective-edit-modal');
            const closeSelectiveModal = document.getElementById('close-selective-modal');
            const selectiveCodeEditor = document.getElementById('selective-code-editor');
            const editInstructions = document.getElementById('edit-instructions');
            const applySelectiveEdit = document.getElementById('apply-selective-edit');
            const cancelSelectiveEdit = document.getElementById('cancel-selective-edit');
            const selectionInfo = document.getElementById('selection-info');
            const selectionText = document.getElementById('selection-text');

            // AI prompt modal elements (modal remains for internal use)
            const aiPromptModal = document.getElementById('ai-prompt-modal');
            const aiUpdatePrompt = document.getElementById('ai-update-prompt');
            const aiCodePreview = document.getElementById('ai-code-preview');
            const applyAiUpdate = document.getElementById('apply-ai-update');
            const cancelAiUpdate = document.getElementById('cancel-ai-update');

            // Import modal functionality
            importButton.addEventListener('click', () => {
                importModal.style.display = 'flex';
                importModal.classList.add('show');
            });

            closeImportModal.addEventListener('click', () => {
                importModal.classList.remove('show');
                setTimeout(() => {
                    importModal.style.display = 'none';
                }, 300);
            });

            // Clear import files functionality
            const clearImportFiles = document.getElementById('clear-import-files');
            clearImportFiles.addEventListener('click', () => {
                // Clear all file inputs in the import modal
                document.getElementById('html-file-modal').value = '';
                document.getElementById('css-file-modal').value = '';
                document.getElementById('js-file-modal').value = '';
                document.getElementById('combined-file-modal').value = '';
                document.getElementById('zip-file-modal').value = '';
                document.getElementById('folder-input-modal').value = '';

                // Show notification
                showNotification('All import file selections cleared!', 'info');
            });

            let typewriterInterval;
            let currentCode = '';
            let selectedCodeRange = { start: 0, end: 0, text: '' };

            // Set initial theme to dark mode by default
            htmlElement.setAttribute('data-theme', 'dark');
            sunIcon.classList.remove('hidden');
            let allowFreshGeneration = true; // Allow first-time generation without pressing New Prompt
            let hasGeneratedOnce = false;    // Track if we've already generated once this session

            moonIcon.classList.add('hidden');

            // Theme toggle functionality
            themeSwitcher.addEventListener('click', (event) => {
                createRipple(event);

                if (htmlElement.getAttribute('data-theme') === 'dark') {
                    htmlElement.setAttribute('data-theme', 'light');
                    sunIcon.classList.add('hidden');
                    moonIcon.classList.remove('hidden');
                } else {
                    htmlElement.setAttribute('data-theme', 'dark');
                    sunIcon.classList.remove('hidden');
                    moonIcon.classList.add('hidden');
                }
            });

            // Typewriter effect function
            function typewriterEffect(text, element, callback) {
                let i = 0;
                element.textContent = ''; // Clear previous content
                clearInterval(typewriterInterval); // Clear any existing interval

                typewriterInterval = setInterval(() => {
                    if (i < text.length) {
                        element.textContent += text.charAt(i);
                        i++;
                        element.scrollTop = element.scrollHeight; // Scroll to bottom
                    } else {
                        clearInterval(typewriterInterval);
                        if (callback) callback();
                    }
                }, 10); // Adjust typing speed here (milliseconds per character)
            }

            // Enhanced live preview update function with visual feedback
            function updateLivePreview(code) {
                try {
                    console.log('🔄 Updating live preview with code length:', code ? code.length : 0);

                    if (!livePreviewIframe) {
                        console.error('❌ Live preview iframe not found!');
                        showNotification('Preview iframe not found!', 'error');
                        return;
                    }

                    // Show status indicator
                    const previewStatus = document.getElementById('preview-status');
                    if (previewStatus) {
                        previewStatus.style.display = 'inline';
                        previewStatus.textContent = '🔄 Updating...';
                    }

                    // Add visual feedback
                    livePreviewIframe.classList.add('updating');

                    // Clear any existing content and sources
                    livePreviewIframe.srcdoc = '';
                    livePreviewIframe.src = 'about:blank';

                    // Wait for clearing, then apply new content
                    setTimeout(() => {
                        if (!code || code.trim() === '') {
                            livePreviewIframe.classList.remove('updating');
                            console.log('✅ Preview cleared (empty code)');
                            return;
                        }

                        // Strategy 1: Standard srcdoc update
                        livePreviewIframe.srcdoc = code;
                        console.log('📝 Applied srcdoc update');

                        // Strategy 2: Data URL fallback for better compatibility
                        setTimeout(() => {
                            try {
                                const dataUrl = 'data:text/html;charset=utf-8,' + encodeURIComponent(code);
                                livePreviewIframe.src = dataUrl;
                                console.log('🔗 Applied data URL fallback');
                            } catch (encodeError) {
                                console.warn('⚠️ Data URL encoding failed:', encodeError);

                                // Strategy 3: Blob URL fallback
                                try {
                                    const blob = new Blob([code], { type: 'text/html;charset=utf-8' });
                                    const blobUrl = URL.createObjectURL(blob);
                                    livePreviewIframe.src = blobUrl;
                                    console.log('🎯 Applied blob URL fallback');

                                    // Clean up blob URL after delay
                                    setTimeout(() => {
                                        URL.revokeObjectURL(blobUrl);
                                        console.log('🧹 Cleaned up blob URL');
                                    }, 10000);
                                } catch (blobError) {
                                    console.error('❌ Blob URL creation failed:', blobError);
                                }
                            }

                            // Remove loading state and hide status
                            setTimeout(() => {
                                livePreviewIframe.classList.remove('updating');
                                const previewStatus = document.getElementById('preview-status');
                                if (previewStatus) {
                                    previewStatus.style.display = 'none';
                                }
                                console.log('✅ Preview update completed');
                            }, 300);

                        }, 150);

                    }, 100);

                } catch (error) {
                    livePreviewIframe.classList.remove('updating');
                    const previewStatus = document.getElementById('preview-status');
                    if (previewStatus) {
                        previewStatus.style.display = 'none';
                    }
                    console.error('❌ Error updating live preview:', error);
                    showNotification('Error updating preview: ' + error.message, 'error');
                }
            }

            // Exponential backoff for API calls
            async function fetchWithExponentialBackoff(url, options, retries = 3, delay = 1000) {
                try {
                    const response = await fetch(url, options);
                    if (!response.ok) {
                        if (response.status === 429 && retries > 0) { // Too Many Requests
                            await new Promise(res => setTimeout(res, delay));
                            return fetchWithExponentialBackoff(url, options, retries - 1, delay * 2);
                        }
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                } catch (error) {
                    console.error('Fetch error:', error);
                    throw error;
                }
            }

            // Generate code functionality
            generateButton.addEventListener('click', async (event) => {
                createRipple(event);

                const prompt = promptInput.value.trim();
                if (!prompt) {
                    codeEditor.textContent = "// Please enter a prompt to generate code.";
                    updateLivePreview('');
                    return;
                }

                // Enforce: Only allow first-time generation automatically, then require New Prompt
                if (!allowFreshGeneration && !currentCode) {
                    showNotification("Generation blocked. Press 'New Prompt' to start a new application.", 'warning');
                    return;
                }
                if (hasGeneratedOnce && !allowFreshGeneration) {
                    showNotification("You've already generated an app. Press 'New Prompt' to start another.", 'warning');
                    return;
                }

                generateButton.disabled = true;
                generateButton.classList.add('loading');
                generateButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Generating...</svg>';
                copyButton.disabled = true; // Disable copy button during generation
                // editButton removed
                selectEditButton.disabled = true; // Disable select edit button during generation
                // AI prompt button removed
                codeEditor.textContent = '// Generating code, please wait...';
                updateLivePreview(''); // Clear preview during generation

                // Get API key from saved settings
                let apiKey = "";
                const savedSettings = localStorage.getItem('jj_code_settings');
                if (savedSettings) {
                    try {
                        const settings = JSON.parse(savedSettings);
                        apiKey = settings.apiKey || "";
                    } catch (error) {
                        console.error('Error loading API key from settings:', error);
                    }
                }

                // Fallback to legacy API key storage
                if (!apiKey) {
                    apiKey = localStorage.getItem('gemini_api_key') || "";
                }

                if (!apiKey) {
                    codeEditor.textContent = "// Error: No API key found. Please add your Gemini API key in Settings.";
                    updateLivePreview('');
                    generateButton.disabled = false;
                    generateButton.classList.remove('loading');
                    generateButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M10 18L14 12L10 6L12 4L18 12L12 20L10 18Z"/>Generate Code</svg>';
                    showNotification('Please add your API key in Settings first!', 'error');
                    return;
                }

                // Get selected model from settings
                let selectedModel = "gemini-2.5-flash-preview-05-20";
                if (savedSettings) {
                    try {
                        const settings = JSON.parse(savedSettings);
                        selectedModel = settings.model || "gemini-2.5-flash-preview-05-20";
                    } catch (error) {
                        console.error('Error loading model from settings:', error);
                    }
                }

                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}:generateContent?key=${apiKey}`;

                const userPrompt = `You are an expert web developer. Generate a single, self-contained HTML file (including all HTML, CSS, and JavaScript within <style> and <script> tags respectively) for a web component based on the following description: "${prompt}".
                Ensure the HTML is complete and directly runnable. Do not include any external links or resources. Provide ONLY the HTML code block, wrapped in a markdown code block (e.g., \`\`\`html...your code...\`\`\`).`;

                const chatHistory = [{ role: "user", parts: [{ text: userPrompt }] }];
                const payload = { contents: chatHistory };

                // Log the API URL and payload for debugging
                console.log('API URL:', apiUrl);
                console.log('API Payload:', JSON.stringify(payload, null, 2));

                try {
                    const result = await fetchWithExponentialBackoff(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        let generatedText = result.candidates[0].content.parts[0].text;

                        // Extract code from markdown block
                        const htmlMatch = generatedText.match(/```html\n([\s\S]*?)\n```/);
                        if (htmlMatch && htmlMatch[1]) {
                            currentCode = htmlMatch[1].trim();
                        } else {
                            // Fallback if markdown block is not found, try to use the raw text
                            currentCode = generatedText.trim();
                            console.warn("Could not find HTML markdown block. Using raw generated text.");
                        }

                        typewriterEffect(currentCode, codeEditor, () => {
                            hasGeneratedOnce = true; // mark that we've generated once in this session
                            updateLivePreview(currentCode);
                            copyButton.disabled = false; // Enable copy button after typewriter effect
                            // editButton removed
                            selectEditButton.disabled = false; // Enable select edit button after typewriter effect
                            downloadButton.disabled = false; // Enable download when code exists
                        });
                    } else {
                        codeEditor.textContent = "// Error: No code generated. Please try a different prompt.";
                        updateLivePreview('');
                    }
                } catch (error) {
                    console.error('Failed to fetch code:', error);
                    codeEditor.textContent = `// Error generating code: ${error.message}. Please check your network or try again.`;
                    updateLivePreview('');


                } finally {
                    generateButton.disabled = false;
                    generateButton.classList.remove('loading');
                    generateButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M10 18L14 12L10 6L12 4L18 12L12 20L10 18Z"/>Generate Code</svg>';
                    // If an error occurred and no code was generated, ensure copy button remains disabled
                    if (!currentCode) {
                        copyButton.disabled = true;
                        // editButton removed
                        selectEditButton.disabled = true;
                        downloadButton.disabled = true;
                    }
                }
            });

            // Add ripple effect to buttons
            function createRipple(event) {
                const button = event.currentTarget;
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = event.clientX - rect.left - size / 2;
                const y = event.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('button-ripple');

                button.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }

            // Enhanced Copy to clipboard functionality
            copyButton.addEventListener('click', (event) => {
                createRipple(event);

                if (currentCode) {
                    // Add loading state
                    copyButton.classList.add('loading');
                    const originalText = copyButton.innerHTML;
                    copyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Copying...</svg>';

                    setTimeout(() => {
                        const tempTextArea = document.createElement('textarea');
                        tempTextArea.value = currentCode;
                        document.body.appendChild(tempTextArea);
                        tempTextArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(tempTextArea);

                        // Add success state
                        copyButton.classList.remove('loading');
                        copyButton.classList.add('success');
                        copyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/></svg>Copied!';

                        // Show success notification
                        showNotification('Code copied to clipboard!', 'success');

                        setTimeout(() => {
                            copyButton.classList.remove('success');
                            copyButton.innerHTML = originalText;
                        }, 2000);
                    }, 300);
                }
            });

            // Edit button functionality removed - Select & Edit provides the same functionality




            // Selective Edit Functionality
            selectEditButton.addEventListener('click', (event) => {
                createRipple(event);

                console.log('Select Edit button clicked, currentCode:', currentCode ? 'exists' : 'empty');

                // For testing, use sample code if no code exists
                if (!currentCode) {
                    currentCode = `<!DOCTYPE html>
<html>
<head>
    <title>Sample Code</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>This is sample code for testing.</p>
</body>
</html>`;
                    showNotification('Using sample code for testing...', 'info');
                }

                // Test notification to verify button works
                showNotification('Select & Edit button clicked! Opening modal...', 'info');

                // Open selective edit modal
                openSelectiveEditModal();
            });

            function openSelectiveEditModal() {
                console.log('Opening enhanced selective edit modal');

                // Populate the textarea with current code
                selectiveCodeEditor.value = currentCode;
                editInstructions.value = '';
                selectedCodeRange = { start: 0, end: 0, text: '' };

                // Update line numbers
                updateLineNumbers();

                // Reset to instructions tab
                document.querySelector('[data-tab="instructions"]').click();

                // Clear find/replace panel
                if (findReplacePanel) findReplacePanel.style.display = 'none';

                // Default: select all so user can just type instructions and apply
                selectiveCodeEditor.setSelectionRange(0, selectiveCodeEditor.value.length);
                updateSelectionInfo();

                console.log('Enhanced modal elements:', {
                    modal: selectiveEditModal,
                    codeEditor: selectiveCodeEditor,
                    instructions: editInstructions,
                    lineNumbers: lineNumbers
                });

                // Show modal
                selectiveEditModal.classList.add('show');
                selectiveEditModal.style.display = 'flex';

                console.log('Enhanced modal should be visible now');

                // Focus on the code editor
                setTimeout(() => {
                    selectiveCodeEditor.focus();
                    debugPreviewState(); // Debug preview state when modal opens
                }, 100);
            }

            function closeSelectiveEditModal() {
                selectiveEditModal.classList.remove('show');
                setTimeout(() => {
                    selectiveEditModal.style.display = 'none';
                }, 300);
            }

            // Handle text selection in selective editor
            selectiveCodeEditor.addEventListener('select', updateSelectionInfo);
            selectiveCodeEditor.addEventListener('mouseup', updateSelectionInfo);
            selectiveCodeEditor.addEventListener('keyup', updateSelectionInfo);

            // Add keyboard shortcuts for enhanced functionality
            selectiveCodeEditor.addEventListener('keydown', (e) => {
                // Ctrl+R or F5 for force refresh preview
                if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                    e.preventDefault();
                    if (forceRefreshBtn) {
                        forceRefreshBtn.click();
                    }
                }

                // Ctrl+F for find/replace
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    if (findReplaceBtn) {
                        findReplaceBtn.click();
                    }
                }

                // Ctrl+A for select all (enhanced)
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    selectiveCodeEditor.select();
                    updateSelectionInfo();
                }
            });

            function updateSelectionInfo() {
                const start = selectiveCodeEditor.selectionStart;
                const end = selectiveCodeEditor.selectionEnd;

                if (start !== end) {
                    selectedCodeRange.start = start;
                    selectedCodeRange.end = end;
                    selectedCodeRange.text = selectiveCodeEditor.value.substring(start, end);

                    const lines = selectedCodeRange.text.split('\n').length;
                    const chars = selectedCodeRange.text.length;

                    selectionText.textContent = `Selected: ${lines} line(s), ${chars} character(s)`;
                    selectionInfo.style.display = 'block';

                    // Update enhanced selection details if they exist
                    const selectionLines = document.getElementById('selection-lines');
                    const selectionChars = document.getElementById('selection-chars');
                    const selectionPosition = document.getElementById('selection-position');

                    if (selectionLines) selectionLines.textContent = `Lines: ${lines}`;
                    if (selectionChars) selectionChars.textContent = `Chars: ${chars}`;
                    if (selectionPosition) selectionPosition.textContent = `Position: ${start}-${end}`;

                    applySelectiveEdit.disabled = false;
                    if (previewChangesBtn) previewChangesBtn.disabled = false;
                } else {
                    selectedCodeRange = { start: 0, end: 0, text: '' };
                    selectionText.textContent = 'No text selected';
                    selectionInfo.style.display = 'none';
                    applySelectiveEdit.disabled = true;
                    if (previewChangesBtn) previewChangesBtn.disabled = true;
                }
            }

            // Enable/disable apply button based on instructions
            editInstructions.addEventListener('input', () => {
                const hasSelection = selectedCodeRange.text.length > 0;
                const hasInstructions = editInstructions.value.trim().length > 0;
                applySelectiveEdit.disabled = !(hasSelection && hasInstructions);
            });

            // Apply selective edit
            applySelectiveEdit.addEventListener('click', async () => {
                console.log('Apply Changes button clicked');
                console.log('Selected code range:', selectedCodeRange);
                console.log('Edit instructions:', editInstructions.value);

                if (!selectedCodeRange.text || !editInstructions.value.trim()) {
                    showNotification('Please select code and enter edit instructions', 'warning');
                    return;
                }

                try {
                    await performSelectiveEdit();
                } catch (error) {
                    console.error('Error in apply selective edit:', error);
                    showNotification('Error applying changes: ' + error.message, 'error');
                }
            });

            async function performSelectiveEdit() {
                console.log('performSelectiveEdit called');
                console.log('selectedCodeRange:', selectedCodeRange);
                console.log('editInstructions value:', editInstructions.value);

                // Get API key from saved settings
                let apiKey = "";
                const savedSettings = localStorage.getItem('jj_code_settings');
                if (savedSettings) {
                    try {
                        const settings = JSON.parse(savedSettings);
                        apiKey = settings.apiKey || "";
                    } catch (e) {
                        console.error('Error parsing saved settings:', e);
                    }
                }

                console.log('API key found:', apiKey ? 'Yes' : 'No');

                if (!apiKey) {
                    // For testing without API key, simulate the edit
                    showNotification('No API key found. Running in test mode...', 'warning');
                    await performTestEdit();
                    return;
                }

                // Show loading state
                applySelectiveEdit.disabled = true;
                applySelectiveEdit.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Processing...</svg>';

                try {
                    const editMode = document.querySelector('input[name="edit-mode"]:checked').value;
                    const preserveFormatting = document.getElementById('preserve-formatting').checked;
                    const addExplanations = document.getElementById('add-explanations').checked;
                    const temperature = parseFloat(document.getElementById('edit-temperature').value);
                    const model = document.getElementById('edit-model-select').value;

                    let prompt = `You are a precise code editor AI. Your task is to modify the selected code according to the user's instructions.

SELECTED CODE TO MODIFY:
` + '```' + `
${selectedCodeRange.text}
` + '```' + `

USER INSTRUCTIONS: ${editInstructions.value.trim()}

EDIT MODE: ${editMode.toUpperCase()}`;

                    switch (editMode) {
                        case 'replace':
                            prompt += `\n- Replace the selected code with the modified version`;
                            break;
                        case 'insert-before':
                            prompt += `\n- Generate code to insert BEFORE the selected code`;
                            break;
                        case 'insert-after':
                            prompt += `\n- Generate code to insert AFTER the selected code`;
                            break;
                        case 'wrap':
                            prompt += `\n- Generate code that wraps around the selected code. Use {{SELECTION}} as placeholder for the original code`;
                            break;
                    }

                    prompt += `

IMPORTANT RULES:
1. Return ONLY the ${editMode === 'replace' ? 'modified version of the selected code' : 'new code to ' + editMode.replace('-', ' ')}
2. ${preserveFormatting ? 'Preserve the exact indentation and formatting style' : 'Use clean, readable formatting'}
3. Keep the same programming language and syntax
4. Make ${temperature > 0.5 ? 'creative but appropriate' : 'minimal and precise'} changes
5. ${addExplanations ? 'Include brief explanatory comments where helpful' : 'Do NOT add explanatory comments unless specifically requested'}
6. Do NOT add markdown formatting or code blocks in your response

${editMode === 'wrap' ? 'Wrapped code (use {{SELECTION}} for original code):' : 'Modified code:'}`;

                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            contents: [{
                                role: "user",
                                parts: [{ text: prompt }]
                            }],
                            generationConfig: {
                                temperature: temperature,
                                topK: 1,
                                topP: 0.8,
                                maxOutputTokens: 8192,
                            }
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    let modifiedCode = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

                    if (!modifiedCode) {
                        throw new Error('No response received from AI');
                    }

                    // Clean up the response - remove any markdown formatting
                    modifiedCode = modifiedCode.replace(/^```[\w]*\n?/, '').replace(/\n?```$/, '').trim();

                    // Save to history before making changes
                    saveToHistory(currentCode, null, selectedCodeRange);

                    // Apply changes based on edit mode
                    const beforeSelection = currentCode.substring(0, selectedCodeRange.start);
                    const afterSelection = currentCode.substring(selectedCodeRange.end);

                    switch (editMode) {
                        case 'replace':
                            currentCode = beforeSelection + modifiedCode + afterSelection;
                            break;
                        case 'insert-before':
                            currentCode = beforeSelection + modifiedCode + '\n' + selectedCodeRange.text + afterSelection;
                            break;
                        case 'insert-after':
                            currentCode = beforeSelection + selectedCodeRange.text + '\n' + modifiedCode + afterSelection;
                            break;
                        case 'wrap':
                            currentCode = beforeSelection + modifiedCode.replace('{{SELECTION}}', selectedCodeRange.text) + afterSelection;
                            break;
                        default:
                            currentCode = beforeSelection + modifiedCode + afterSelection;
                    }

                    // Update history with final result
                    editHistory[editHistory.length - 1].afterCode = currentCode;

                    // Update the main code editor
                    codeEditor.textContent = currentCode;

                    // Force update live preview with enhanced refresh
                    console.log('Forcing live preview update after selective edit');
                    updateLivePreview(currentCode);

                    // Additional fallback refresh with emergency sync
                    setTimeout(() => {
                        if (livePreviewIframe && currentCode) {
                            livePreviewIframe.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(currentCode);
                            console.log('Applied fallback iframe refresh');

                            // Emergency sync as final fallback
                            setTimeout(() => {
                                if (!livePreviewIframe.contentDocument?.body?.innerHTML) {
                                    console.log('🚨 Preview still not showing, using emergency sync');
                                    syncPreviewWithCode();
                                }
                                debugPreviewState(); // Debug after all attempts
                            }, 500);
                        }
                    }, 100);

                    // Close modal and show success
                    closeSelectiveEditModal();
                    showNotification('Selected code modified successfully! Preview updated.', 'success');

                } catch (error) {
                    console.error('Error in selective edit:', error);
                    showNotification(`Selective edit failed: ${error.message}`, 'error');
                } finally {
                    // Reset button
                    applySelectiveEdit.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/>Apply Changes</svg>';
                    applySelectiveEdit.disabled = false;
                }
            }

            // Test function for when no API key is available
            async function performTestEdit() {
                console.log('Running test edit mode');

                // Show loading state
                applySelectiveEdit.disabled = true;
                applySelectiveEdit.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Testing...</svg>';

                try {
                    // Simulate processing delay
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Create a simple test modification
                    let modifiedCode = selectedCodeRange.text;
                    const instructions = editInstructions.value.toLowerCase();

                    // Simple test modifications based on common instructions
                    if (instructions.includes('hello') || instructions.includes('welcome')) {
                        modifiedCode = modifiedCode.replace(/Hello World/gi, 'Welcome');
                        modifiedCode = modifiedCode.replace(/hello/gi, 'welcome');
                    } else if (instructions.includes('blue') || instructions.includes('color')) {
                        modifiedCode = modifiedCode.replace(/color:\s*[^;]+/gi, 'color: blue');
                        if (!modifiedCode.includes('color:')) {
                            modifiedCode = modifiedCode.replace(/<([^>]+)>/g, '<$1 style="color: blue">');
                        }
                    } else if (instructions.includes('comment')) {
                        modifiedCode = `<!-- Modified: ${editInstructions.value} -->\n${modifiedCode}`;
                    } else {
                        // Generic modification - add a comment
                        modifiedCode = `<!-- TEST EDIT: ${editInstructions.value} -->\n${modifiedCode}`;
                    }

                    console.log('Original code:', selectedCodeRange.text);
                    console.log('Modified code:', modifiedCode);

                    // Replace the selected text with the modified version in the original code
                    const beforeSelection = currentCode.substring(0, selectedCodeRange.start);
                    const afterSelection = currentCode.substring(selectedCodeRange.end);

                    currentCode = beforeSelection + modifiedCode + afterSelection;

                    // Update the main code editor
                    codeEditor.textContent = currentCode;

                    // Force update live preview with enhanced refresh
                    console.log('Forcing live preview update after test edit');
                    updateLivePreview(currentCode);

                    // Additional fallback refresh with emergency sync
                    setTimeout(() => {
                        if (livePreviewIframe && currentCode) {
                            livePreviewIframe.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(currentCode);
                            console.log('Applied fallback iframe refresh for test edit');

                            // Emergency sync as final fallback
                            setTimeout(() => {
                                if (!livePreviewIframe.contentDocument?.body?.innerHTML) {
                                    console.log('🚨 Test edit preview not showing, using emergency sync');
                                    syncPreviewWithCode();
                                }
                            }, 500);
                        }
                    }, 100);

                    // Close modal and show success
                    closeSelectiveEditModal();
                    showNotification('Test edit completed successfully! Preview updated. (Configure API key for AI-powered edits)', 'success');

                } catch (error) {
                    console.error('Error in test edit:', error);
                    showNotification(`Test edit failed: ${error.message}`, 'error');
                } finally {
                    // Reset button
                    applySelectiveEdit.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/>Apply Changes</svg>';
                    applySelectiveEdit.disabled = false;
                }
            }

            // Modal close handlers
            closeSelectiveModal.addEventListener('click', closeSelectiveEditModal);
            cancelSelectiveEdit.addEventListener('click', closeSelectiveEditModal);

            // Close modal when clicking outside
            selectiveEditModal.addEventListener('click', (e) => {
                if (e.target === selectiveEditModal) {
                    closeSelectiveEditModal();
                }
            });

            // Force refresh preview button
            if (forceRefreshBtn) {
                forceRefreshBtn.addEventListener('click', () => {
                    console.log('Force refresh button clicked');

                    if (!currentCode || currentCode.trim() === '') {
                        showNotification('No code to preview', 'warning');
                        return;
                    }

                    console.log('Forcing preview refresh with current code...');

                    // Apply all refresh strategies
                    updateLivePreview(currentCode);

                    // Additional manual refresh
                    setTimeout(() => {
                        debugPreviewState();
                    }, 500);

                    showNotification('Preview force refreshed! Check console for debug info.', 'info');
                });
            }

            // Enhanced Select & Edit functionality
            let editHistory = [];
            let editHistoryIndex = -1;
            let currentSelection = null;

            // Get new enhanced elements
            const undoEditBtn = document.getElementById('undo-edit');
            const redoEditBtn = document.getElementById('redo-edit');
            const selectAllCodeBtn = document.getElementById('select-all-code');
            const findReplaceBtn = document.getElementById('find-replace-btn');
            const lineNumbersToggle = document.getElementById('line-numbers-toggle');
            const previewChangesBtn = document.getElementById('preview-changes');
            const forceRefreshBtn = document.getElementById('force-refresh-preview');
            const lineNumbers = document.getElementById('line-numbers');
            const findReplacePanel = document.getElementById('find-replace-panel');

            // Tab functionality
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const targetTab = btn.dataset.tab;

                    // Update active tab
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    btn.classList.add('active');
                    document.getElementById(`${targetTab}-tab`).classList.add('active');
                });
            });

            // Line numbers functionality
            function updateLineNumbers() {
                if (!lineNumbers) return;

                const lines = selectiveCodeEditor.value.split('\n');
                const lineNumbersHtml = lines.map((_, index) =>
                    `<div>${index + 1}</div>`
                ).join('');
                lineNumbers.innerHTML = lineNumbersHtml;
            }

            lineNumbersToggle.addEventListener('click', () => {
                lineNumbers.style.display = lineNumbers.style.display === 'none' ? 'block' : 'none';
                lineNumbersToggle.classList.toggle('active');
            });

            // Update line numbers when code changes
            selectiveCodeEditor.addEventListener('input', updateLineNumbers);

            // Select all functionality
            selectAllCodeBtn.addEventListener('click', () => {
                selectiveCodeEditor.select();
                updateSelectionInfo();
                showNotification('All code selected', 'info');
            });

            // Find & Replace functionality
            findReplaceBtn.addEventListener('click', () => {
                findReplacePanel.style.display = findReplacePanel.style.display === 'none' ? 'block' : 'none';
                if (findReplacePanel.style.display !== 'none') {
                    document.getElementById('find-input').focus();
                }
            });

            document.getElementById('close-find-replace').addEventListener('click', () => {
                findReplacePanel.style.display = 'none';
            });

            // Quick Actions functionality
            const quickActionBtns = document.querySelectorAll('.quick-action-btn');
            quickActionBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.dataset.action;
                    applyQuickAction(action);
                });
            });

            function applyQuickAction(action) {
                const selection = getSelectedText();
                if (!selection.text) {
                    showNotification('Please select some code first', 'warning');
                    return;
                }

                let instructions = '';
                switch (action) {
                    case 'add-comments':
                        instructions = 'Add clear, helpful comments to explain what this code does';
                        break;
                    case 'remove-comments':
                        instructions = 'Remove all comments from this code while preserving functionality';
                        break;
                    case 'format-code':
                        instructions = 'Format this code with proper indentation and spacing';
                        break;
                    case 'minify-code':
                        instructions = 'Minify this code by removing unnecessary whitespace and comments';
                        break;
                    case 'add-error-handling':
                        instructions = 'Add comprehensive error handling and try-catch blocks';
                        break;
                    case 'make-async':
                        instructions = 'Convert this code to use async/await pattern where appropriate';
                        break;
                    case 'add-logging':
                        instructions = 'Add console.log statements for debugging and monitoring';
                        break;
                    case 'optimize-performance':
                        instructions = 'Optimize this code for better performance and efficiency';
                        break;
                }

                document.getElementById('edit-instructions').value = instructions;
                document.querySelector('[data-tab="instructions"]').click();
                showNotification(`Quick action applied: ${action.replace('-', ' ')}`, 'success');
            }



            function getSelectedText() {
                const start = selectiveCodeEditor.selectionStart;
                const end = selectiveCodeEditor.selectionEnd;
                const text = selectiveCodeEditor.value.substring(start, end);
                return { text, start, end };
            }

            // Debug function for preview issues
            function debugPreviewState() {
                console.log('=== PREVIEW DEBUG INFO ===');
                console.log('currentCode length:', currentCode ? currentCode.length : 'null/undefined');
                console.log('livePreviewIframe exists:', !!livePreviewIframe);
                console.log('iframe srcdoc length:', livePreviewIframe?.srcdoc?.length || 'none');
                console.log('iframe src:', livePreviewIframe?.src || 'none');
                console.log('iframe contentDocument:', !!livePreviewIframe?.contentDocument);
                console.log('iframe body content:', livePreviewIframe?.contentDocument?.body?.innerHTML?.length || 'none');
                console.log('========================');

                // Try to force refresh if there's an issue
                if (currentCode && livePreviewIframe && (!livePreviewIframe.srcdoc || livePreviewIframe.srcdoc.length === 0)) {
                    console.log('Detected preview issue, attempting auto-fix...');
                    updateLivePreview(currentCode);
                }
            }

            // Sync preview with current code (emergency function)
            function syncPreviewWithCode() {
                console.log('🔄 Emergency preview sync initiated');

                if (!currentCode) {
                    console.log('❌ No current code to sync');
                    return false;
                }

                if (!livePreviewIframe) {
                    console.log('❌ No preview iframe found');
                    return false;
                }

                try {
                    // Force complete refresh
                    livePreviewIframe.src = 'about:blank';

                    setTimeout(() => {
                        livePreviewIframe.srcdoc = currentCode;
                        console.log('✅ Emergency sync completed via srcdoc');

                        setTimeout(() => {
                            if (!livePreviewIframe.contentDocument?.body?.innerHTML) {
                                livePreviewIframe.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(currentCode);
                                console.log('✅ Emergency sync completed via data URL');
                            }
                        }, 200);
                    }, 100);

                    return true;
                } catch (error) {
                    console.error('❌ Emergency sync failed:', error);
                    return false;
                }
            }

            // History management
            function saveToHistory(beforeCode, afterCode, selection) {
                editHistory = editHistory.slice(0, editHistoryIndex + 1);
                editHistory.push({ beforeCode, afterCode, selection });
                editHistoryIndex = editHistory.length - 1;
                updateHistoryButtons();
            }

            function updateHistoryButtons() {
                undoEditBtn.disabled = editHistoryIndex < 0;
                redoEditBtn.disabled = editHistoryIndex >= editHistory.length - 1;
            }

            undoEditBtn.addEventListener('click', () => {
                if (editHistoryIndex >= 0) {
                    const historyItem = editHistory[editHistoryIndex];
                    selectiveCodeEditor.value = historyItem.beforeCode;
                    editHistoryIndex--;
                    updateHistoryButtons();
                    updateLineNumbers();
                    showNotification('Edit undone', 'info');
                }
            });

            redoEditBtn.addEventListener('click', () => {
                if (editHistoryIndex < editHistory.length - 1) {
                    editHistoryIndex++;
                    const historyItem = editHistory[editHistoryIndex];
                    selectiveCodeEditor.value = historyItem.afterCode;
                    updateHistoryButtons();
                    updateLineNumbers();
                    showNotification('Edit redone', 'info');
                }
            });

            // Preview functionality
            previewChangesBtn.addEventListener('click', async () => {
                const selection = getSelectedText();
                const instructions = document.getElementById('edit-instructions').value.trim();

                if (!instructions) {
                    showNotification('Please enter edit instructions first', 'warning');
                    return;
                }

                // Show preview tab
                document.querySelector('[data-tab="preview"]').click();

                // Update before preview
                document.getElementById('preview-before').textContent = selection.text || selectiveCodeEditor.value;

                // Generate preview (simplified for demo)
                document.getElementById('preview-after').textContent = 'Preview will be generated here...';

                showNotification('Preview generated! Check the Preview tab', 'success');
            });

            // Force refresh preview functionality
            if (forceRefreshBtn) {
                forceRefreshBtn.addEventListener('click', () => {
                    console.log('Force refreshing live preview...');

                    if (!currentCode) {
                        showNotification('No code to preview', 'warning');
                        return;
                    }

                    // Multiple refresh strategies
                    try {
                        // Strategy 1: Clear and reset srcdoc
                        livePreviewIframe.srcdoc = '';
                        setTimeout(() => {
                            livePreviewIframe.srcdoc = currentCode;
                        }, 100);

                        // Strategy 2: Use data URL as fallback
                        setTimeout(() => {
                            livePreviewIframe.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(currentCode);
                        }, 200);

                        // Strategy 3: Force reload
                        setTimeout(() => {
                            if (livePreviewIframe.contentWindow) {
                                livePreviewIframe.contentWindow.location.reload();
                            }
                        }, 300);

                        showNotification('Live preview force refreshed!', 'success');

                    } catch (error) {
                        console.error('Error force refreshing preview:', error);
                        showNotification('Error refreshing preview: ' + error.message, 'error');
                    }
                });
            }

            // Find & Replace functionality
            let findMatches = [];
            let currentMatchIndex = -1;

            function findInCode(searchTerm, caseSensitive = false) {
                const code = selectiveCodeEditor.value;
                const flags = caseSensitive ? 'g' : 'gi';
                const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);

                findMatches = [];
                let match;
                while ((match = regex.exec(code)) !== null) {
                    findMatches.push({
                        start: match.index,
                        end: match.index + match[0].length,
                        text: match[0]
                    });
                }

                currentMatchIndex = findMatches.length > 0 ? 0 : -1;
                return findMatches;
            }

            function highlightMatch(matchIndex) {
                if (matchIndex >= 0 && matchIndex < findMatches.length) {
                    const match = findMatches[matchIndex];
                    selectiveCodeEditor.setSelectionRange(match.start, match.end);
                    selectiveCodeEditor.focus();
                }
            }

            document.getElementById('find-next').addEventListener('click', () => {
                const searchTerm = document.getElementById('find-input').value;
                if (!searchTerm) return;

                if (findMatches.length === 0) {
                    findInCode(searchTerm);
                }

                if (findMatches.length > 0) {
                    currentMatchIndex = (currentMatchIndex + 1) % findMatches.length;
                    highlightMatch(currentMatchIndex);
                    showNotification(`Match ${currentMatchIndex + 1} of ${findMatches.length}`, 'info');
                } else {
                    showNotification('No matches found', 'warning');
                }
            });

            document.getElementById('find-prev').addEventListener('click', () => {
                const searchTerm = document.getElementById('find-input').value;
                if (!searchTerm) return;

                if (findMatches.length === 0) {
                    findInCode(searchTerm);
                }

                if (findMatches.length > 0) {
                    currentMatchIndex = currentMatchIndex <= 0 ? findMatches.length - 1 : currentMatchIndex - 1;
                    highlightMatch(currentMatchIndex);
                    showNotification(`Match ${currentMatchIndex + 1} of ${findMatches.length}`, 'info');
                } else {
                    showNotification('No matches found', 'warning');
                }
            });

            document.getElementById('replace-one').addEventListener('click', () => {
                const findText = document.getElementById('find-input').value;
                const replaceText = document.getElementById('replace-input').value;

                if (!findText) return;

                if (currentMatchIndex >= 0 && currentMatchIndex < findMatches.length) {
                    const match = findMatches[currentMatchIndex];
                    const beforeCode = selectiveCodeEditor.value;
                    const afterCode = beforeCode.substring(0, match.start) + replaceText + beforeCode.substring(match.end);

                    selectiveCodeEditor.value = afterCode;
                    updateLineNumbers();

                    // Update find matches
                    findInCode(findText);
                    showNotification('Replaced 1 occurrence', 'success');
                }
            });

            document.getElementById('replace-all').addEventListener('click', () => {
                const findText = document.getElementById('find-input').value;
                const replaceText = document.getElementById('replace-input').value;

                if (!findText) return;

                const beforeCode = selectiveCodeEditor.value;
                const afterCode = beforeCode.replace(new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceText);

                if (beforeCode !== afterCode) {
                    selectiveCodeEditor.value = afterCode;
                    updateLineNumbers();

                    const count = (beforeCode.match(new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;
                    showNotification(`Replaced ${count} occurrences`, 'success');
                } else {
                    showNotification('No matches found to replace', 'warning');
                }
            });



            // AI Prompt button removed per request (modal still available for internal triggers)
            // aiPromptButton click handler removed.

            function openAiPromptModal() {
                console.log('Opening AI prompt modal');

                // Populate the code preview with current code (truncated for display)
                const previewCode = currentCode.length > 500 ?
                    currentCode.substring(0, 500) + '\n\n... (truncated)' :
                    currentCode;
                aiCodePreview.textContent = previewCode;

                // Clear the prompt
                aiUpdatePrompt.value = '';
                updateAiPromptButton();

                // Show modal
                aiPromptModal.classList.add('show');
                aiPromptModal.style.display = 'flex';

                // Focus on the prompt textarea
                setTimeout(() => {
                    aiUpdatePrompt.focus();
                }, 100);
            }

            function closeAiPromptModal() {
                aiPromptModal.classList.remove('show');
                setTimeout(() => {
                    aiPromptModal.style.display = 'none';
                }, 300);
            }

            function updateAiPromptButton() {
                const hasPrompt = aiUpdatePrompt.value.trim().length > 0;
                applyAiUpdate.disabled = !hasPrompt;
            }

            // Enable/disable apply button based on prompt
            aiUpdatePrompt.addEventListener('input', updateAiPromptButton);

            // Suggestion buttons functionality
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('suggestion-btn')) {
                    const prompt = e.target.getAttribute('data-prompt');
                    aiUpdatePrompt.value = prompt;
                    updateAiPromptButton();
                    aiUpdatePrompt.focus();
                }
            });

            // Apply AI update
            applyAiUpdate.addEventListener('click', async () => {
                console.log('Apply AI Update button clicked');

                if (!aiUpdatePrompt.value.trim()) {
                    showNotification('Please enter update instructions', 'warning');
                    return;
                }

                try {
                    await performAiUpdate();
                } catch (error) {
                    console.error('Error in AI update:', error);
                    showNotification('Error applying AI update: ' + error.message, 'error');
                }
            });

            async function performAiUpdate() {
                console.log('performAiUpdate called');
                console.log('Update prompt:', aiUpdatePrompt.value);

                // Get API key from saved settings
                let apiKey = "";
                const savedSettings = localStorage.getItem('jj_code_settings');
                if (savedSettings) {
                    try {
                        const settings = JSON.parse(savedSettings);
                        apiKey = settings.apiKey || "";
                    } catch (e) {
                        console.error('Error parsing saved settings:', e);
                    }
                }

                console.log('API key found:', apiKey ? 'Yes' : 'No');

                if (!apiKey) {
                    // For testing without API key, simulate the update
                    showNotification('No API key found. Running in test mode...', 'warning');
                    await performTestAiUpdate();
                    return;
                }

                // Show loading state
                applyAiUpdate.disabled = true;
                applyAiUpdate.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Processing...</svg>';

                try {
                    const minimalModeEl = document.getElementById('ai-minimal-mode');
                    const minimalMode = minimalModeEl ? minimalModeEl.checked : true;
                    const prompt = minimalMode
                        ? `You are an expert web developer. Modify the provided HTML/JS/CSS in-place according to the user's instructions.

STRICT REQUIREMENTS:
- Work ONLY within the provided code. Do not generate a different app.
- Apply MINIMAL changes in-place. Preserve existing structure, classes, and IDs unless a change is explicitly required.
- Keep ALL existing functionality unless asked to remove/change it.
- Maintain the same file structure and include only inline assets.
- Return ONLY the complete updated code (no markdown, no explanations).

CURRENT CODE:
` + '```' + `
${currentCode}
` + '```' + `

USER INSTRUCTIONS: ${aiUpdatePrompt.value.trim()}

If the user's request is ambiguous or risky, add a short HTML comment near the change explaining the assumption.

Output: the full updated code only.`
                        : `You are an expert web developer. Update the following code based on the user's instructions. Return only the complete updated code without any explanations or markdown formatting.

CURRENT CODE:
` + '```' + `
${currentCode}
` + '```' + `

USER INSTRUCTIONS: ${aiUpdatePrompt.value.trim()}

IMPORTANT RULES:
1. Return only the complete updated code
2. Maintain the same file structure and format
3. Keep all existing functionality unless specifically asked to change it
4. Add the requested features/changes seamlessly
5. Ensure the code is clean, well-formatted, and functional
6. Do NOT include any explanations or markdown formatting`;

                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            contents: [{
                                role: "user",
                                parts: [{ text: prompt }]
                            }],
                            generationConfig: {
                                temperature: 0.2,
                                topK: 40,
                                topP: 0.95,
                                maxOutputTokens: 8192,
                            }
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    let updatedCode = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

                    if (!updatedCode) {
                        throw new Error('No response received from AI');
                    }

                    // Clean up the response - remove any markdown formatting
                    updatedCode = updatedCode.replace(/^```[\w]*\n?/, '').replace(/\n?```$/, '').trim();

                    // Update the current code
                    currentCode = updatedCode;

                    // Update the main code editor with typewriter effect
                    typewriterEffect(currentCode, codeEditor, () => {
                        updateLivePreview(currentCode);
                    });

                    // Close modal and show success
                    closeAiPromptModal();
                    showNotification('Code updated successfully with AI!', 'success');

                } catch (error) {
                    console.error('Error in AI update:', error);
                    showNotification(`AI update failed: ${error.message}`, 'error');
                } finally {
                    // Reset button
                    applyAiUpdate.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Update Code with AI</svg>';
                    applyAiUpdate.disabled = false;
                }
            }

            // Test function for AI update when no API key is available
            async function performTestAiUpdate() {
                console.log('Running test AI update mode');

                // Show loading state
                applyAiUpdate.disabled = true;
                applyAiUpdate.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Testing...</svg>';

                try {
                    // Simulate processing delay
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Create a simple test modification based on the prompt
                    let updatedCode = currentCode;
                    const instructions = aiUpdatePrompt.value.toLowerCase();

                    // Simple test modifications based on common instructions
                    if (instructions.includes('dark mode') || instructions.includes('dark theme')) {
                        // Add a simple dark mode toggle
                        const darkModeCSS = `
        /* Dark Mode Styles */
        .dark-mode {
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .dark-mode button {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
        }`;
                        updatedCode = updatedCode.replace('</style>', darkModeCSS + ' </style>');

                        // Add dark mode JavaScript (simplified)
                        updatedCode = updatedCode.replace('</' + 'script>', ' function toggleDarkMode(){document.body.classList.toggle("dark-mode");} document.addEventListener("click", function(e){ if(e.target && e.target.id==="darkModeBtn"){ toggleDarkMode(); } }); </' + 'script>');

                        // Add toggle button
                        updatedCode = updatedCode.replace('<body>', '<body> <button id="darkModeBtn">Dark Mode</button>');
                    } else if (instructions.includes('responsive') || instructions.includes('mobile')) {
                        // Add responsive CSS
                        const responsiveCSS = `
        /* Responsive Design */
        @media (max-width: 768px) {
            body { padding: 10px; }
            h1 { font-size: 1.5rem; }
            button { width: 100%; margin: 5px 0; }
        }`;
                        updatedCode = updatedCode.replace('</style>', responsiveCSS + '\n    </style>');
                    } else if (instructions.includes('animation') || instructions.includes('transition')) {
                        // Add animations
                        const animationCSS = `
        /* Animations */
        * { transition: all 0.3s ease; }
        button:hover { transform: scale(1.05); }
        h1 { animation: fadeIn 1s ease-in; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }`;
                        updatedCode = updatedCode.replace('</style>', animationCSS + '\n    </style>');
                    } else {
                        // Generic modification - add a comment about the update
                        updatedCode = updatedCode.replace('<body>', `<body>\n    <!-- Updated with AI: ${aiUpdatePrompt.value} -->`);
                    }

                    console.log('Test AI update completed');

                    // Update the current code
                    currentCode = updatedCode;

                    // Update the main code editor
                    codeEditor.textContent = currentCode;
                    updateLivePreview(currentCode);

                    // Close modal and show success
                    closeAiPromptModal();
                    showNotification('Test AI update completed! (Configure API key for real AI updates)', 'success');

                } catch (error) {
                    console.error('Error in test AI update:', error);
                    showNotification(`Test AI update failed: ${error.message}`, 'error');
                } finally {
                    // Reset button
                    applyAiUpdate.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Update Code with AI</svg>';
                    applyAiUpdate.disabled = false;
                }
            }

            // AI Prompt modal close handlers
            document.getElementById('close-ai-prompt-modal').addEventListener('click', closeAiPromptModal);
            cancelAiUpdate.addEventListener('click', closeAiPromptModal);

            // Close modal when clicking outside
            aiPromptModal.addEventListener('click', (e) => {
                if (e.target === aiPromptModal) {
                    closeAiPromptModal();
                }
            });

            // Enhanced Clear editor functionality
            clearButton.addEventListener('click', (event) => {
                createRipple(event);

                // Add confirmation for clear action
                if (currentCode && currentCode.trim() !== '') {
                    if (confirm('Are you sure you want to clear the code editor? This action cannot be undone.')) {
                        clearCodeEditor();
                    }
                } else {
                    clearCodeEditor();
                }
            });

            function clearCodeEditor() {
                // Add loading state
                clearButton.classList.add('loading');
                const originalText = clearButton.innerHTML;
                clearButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Clearing...</svg>';

                setTimeout(() => {
                    codeEditor.textContent = '// Your generated code will appear here...';
                    updateLivePreview('');
                    currentCode = '';
                    clearInterval(typewriterInterval);
                    copyButton.disabled = true; // Disable copy button when clearing
                    // editButton removed
                    selectEditButton.disabled = true; // Disable select edit button when clearing
                    // AI prompt button removed

                    // Remove loading state
                    clearButton.classList.remove('loading');
                    clearButton.innerHTML = originalText;

                    // Show success notification
                    showNotification('Code editor cleared!', 'info');
                }, 300);
            }

            // Notification system
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <svg class="notification-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            ${type === 'success' ? '<path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/>' :
                              type === 'error' ? '<path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"/>' :
                              '<path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>'}
                        </svg>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.add('show');
                }, 10);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // Refresh preview functionality
            refreshButton.addEventListener('click', (event) => {
                createRipple(event);
                if (currentCode) {
                    updateLivePreview(currentCode);
                }
            });

            // Fullscreen preview functionality (simple version)
            fullscreenButton.addEventListener('click', (event) => {
                createRipple(event);
                if (livePreviewIframe.requestFullscreen) {
                    livePreviewIframe.requestFullscreen();
                } else if (livePreviewIframe.mozRequestFullScreen) { /* Firefox */
                    livePreviewIframe.mozRequestFullScreen();
                } else if (livePreviewIframe.webkitRequestFullscreen) { /* Chrome, Safari and Opera */
                    livePreviewIframe.webkitRequestFullscreen();
                } else if (livePreviewIframe.msRequestFullscreen) { /* IE/Edge */
                    livePreviewIframe.msRequestFullscreen();
                }
            });

            // --- Enhanced Settings Panel Functionality ---

            // Settings panel functionality
            function openSettingsPanel() {
                settingsPanel.classList.add('open');
                settingsOverlay.classList.add('open');
                document.body.style.overflow = 'hidden';

                // Load saved settings
                loadSavedSettings();
            }

            function loadSavedSettings() {
                const savedSettings = localStorage.getItem('jj_code_settings');
                const savedKey = localStorage.getItem('gemini_api_key');

                if (savedSettings) {
                    try {
                        const settings = JSON.parse(savedSettings);

                        // Load API key
                        const apiKeyInput = document.getElementById('api-key-input');
                        if (apiKeyInput) {
                            apiKeyInput.value = settings.apiKey || savedKey || '';
                        }

                        // Update legacy API key storage for backward compatibility
                        if (settings.apiKey) {
                            localStorage.setItem('gemini_api_key', settings.apiKey);
                        }

                        // Load theme
                        if (settings.theme) {
                            htmlElement.setAttribute('data-theme', settings.theme);
                            if (settings.theme === 'light') {
                                sunIcon.classList.add('hidden');
                                moonIcon.classList.remove('hidden');
                            } else {
                                sunIcon.classList.remove('hidden');
                                moonIcon.classList.add('hidden');
                            }
                        }

                        // Load model selection
                        const modelSelect = document.getElementById('model-select');
                        if (modelSelect && settings.model) {
                            modelSelect.value = settings.model;
                        }

                        // Load temperature
                        const temperatureInput = document.getElementById('temperature-input');
                        if (temperatureInput && settings.temperature) {
                            temperatureInput.value = settings.temperature;
                        }

                    } catch (error) {
                        console.error('Error loading saved settings:', error);
                        // Fallback to just loading API key
                        const apiKeyInput = document.getElementById('api-key-input');
                        if (apiKeyInput && savedKey) {
                            apiKeyInput.value = savedKey;
                        }
                    }
                } else {
                    // Fallback to just loading API key
                    const apiKeyInput = document.getElementById('api-key-input');
                    if (apiKeyInput && savedKey) {
                        apiKeyInput.value = savedKey;
                    }
                }
            }

            function closeSettingsPanel() {
                settingsPanel.classList.remove('open');
                settingsOverlay.classList.remove('open');
                document.body.style.overflow = '';
            }

            // Settings button click
            settingsButton.addEventListener('click', (event) => {
                createRipple(event);
                openSettingsPanel();
            });

            // Close settings panel
            settingsClose.addEventListener('click', closeSettingsPanel);
            settingsOverlay.addEventListener('click', closeSettingsPanel);

            // Help button functionality
            helpButton.addEventListener('click', (event) => {
                createRipple(event);
                alert('JJ Code AI Builder Help\n\n1. Enter your description in the text area\n2. Click "Generate Code" to create your component\n3. Use the copy button to copy the generated code\n4. Preview your code in the live preview panel\n5. Access settings to configure your API key');
            });

            // API Key toggle functionality
            const toggleApiKeyButton = document.getElementById('toggle-api-key');
            const apiKeyInput = document.getElementById('api-key-input');

            toggleApiKeyButton.addEventListener('click', () => {
                if (apiKeyInput.type === 'password') {
                    apiKeyInput.type = 'text';
                    toggleApiKeyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="margin-right: 0.25rem;"><path d="M12 4.5C7 4.5 2.73 7.61 1 11.5C2.73 15.39 7 18.5 12 18.5C17 18.5 21.27 15.39 23 11.5C21.27 7.61 17 4.5 12 4.5ZM12 16.5C9.24 16.5 7 14.26 7 11.5C7 8.74 9.24 6.5 12 6.5C14.76 6.5 17 8.74 17 11.5C17 14.26 14.76 16.5 12 16.5ZM12 8.5C10.34 8.5 9 9.84 9 11.5C9 13.16 10.34 14.5 12 14.5C13.66 14.5 15 13.16 15 11.5C15 9.84 13.66 8.5 12 8.5Z"/></svg>Hide';
                } else {
                    apiKeyInput.type = 'password';
                    toggleApiKeyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="margin-right: 0.25rem;"><path d="M12 4.5C7 4.5 2.73 7.61 1 11.5C2.73 15.39 7 18.5 12 18.5C17 18.5 21.27 15.39 23 11.5C21.27 7.61 17 4.5 12 4.5ZM12 16.5C9.24 16.5 7 14.26 7 11.5C7 8.74 9.24 6.5 12 6.5C14.76 6.5 17 8.74 17 11.5C17 14.26 14.76 16.5 12 16.5ZM12 8.5C10.34 8.5 9 9.84 9 11.5C9 13.16 10.34 14.5 12 14.5C13.66 14.5 15 13.16 15 11.5C15 9.84 13.66 8.5 12 8.5Z"/></svg>Show';
                }
            });

            // Test API Key functionality
            const testApiKeyButton = document.getElementById('test-api-key');
            testApiKeyButton.addEventListener('click', async () => {
                const apiKey = apiKeyInput.value.trim();
                if (!apiKey) {
                    showNotification('Please enter an API key first', 'error');
                    return;
                }

                testApiKeyButton.textContent = 'Testing...';
                testApiKeyButton.disabled = true;

                try {
                    // Test the API key with a simple request
                    const testUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;
                    const testPayload = {
                        contents: [{
                            role: "user",
                            parts: [{ text: "Hello, this is a test." }]
                        }]
                    };

                    const response = await fetch(testUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testPayload)
                    });

                    if (response.ok) {
                        showNotification('API key is valid and working!', 'success');
                    } else {
                        const errorData = await response.json();
                        showNotification(`API key test failed: ${errorData.error?.message || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    showNotification(`API key test failed: ${error.message}`, 'error');
                } finally {
                    testApiKeyButton.textContent = 'Test Key';
                    testApiKeyButton.disabled = false;
                }
            });

            // Theme buttons in settings
            const themeDarkButton = document.getElementById('theme-dark');
            const themeLightButton = document.getElementById('theme-light');

            themeDarkButton.addEventListener('click', () => {
                htmlElement.setAttribute('data-theme', 'dark');
                sunIcon.classList.remove('hidden');
                moonIcon.classList.add('hidden');
            });

            themeLightButton.addEventListener('click', () => {
                htmlElement.setAttribute('data-theme', 'light');
                sunIcon.classList.add('hidden');
                moonIcon.classList.remove('hidden');
            });

            // Save Settings Button Functionality
            saveSettingsButton.addEventListener('click', (event) => {
                createRipple(event);

                // Add loading state
                saveSettingsButton.classList.add('loading');
                const originalText = saveSettingsButton.innerHTML;
                saveSettingsButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Saving...</svg>';

                setTimeout(() => {
                    // Save all settings to localStorage
                    const apiKey = document.getElementById('api-key-input').value.trim();
                    const settings = {
                        apiKey: apiKey,
                        theme: htmlElement.getAttribute('data-theme'),
                        model: document.getElementById('model-select').value,
                        temperature: document.getElementById('temperature-input').value,
                        timestamp: new Date().toISOString()
                    };

                    localStorage.setItem('jj_code_settings', JSON.stringify(settings));

                    // Also save API key separately for backward compatibility
                    if (apiKey) {
                        localStorage.setItem('gemini_api_key', apiKey);
                    } else {
                        localStorage.removeItem('gemini_api_key');
                    }

                    // Remove loading state and show success
                    saveSettingsButton.classList.remove('loading');
                    saveSettingsButton.classList.add('success');
                    saveSettingsButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"/></svg>Saved!';

                    // Show success notification
                    showNotification('Settings saved successfully!', 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        saveSettingsButton.classList.remove('success');
                        saveSettingsButton.innerHTML = originalText;
                    }, 2000);

                }, 800);
            });

            // Reset Settings Button Functionality
            resetSettingsButton.addEventListener('click', (event) => {
                createRipple(event);

                // Add confirmation dialog
                if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
                    // Add loading state
                    resetSettingsButton.classList.add('loading');
                    const originalText = resetSettingsButton.innerHTML;
                    resetSettingsButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Resetting...</svg>';

                    setTimeout(() => {
                        // Reset to default values
                        document.getElementById('api-key-input').value = '';
                        document.getElementById('model-select').value = 'gemini-2.5-flash-preview-05-20';
                        document.getElementById('temperature-input').value = '0.7';

                        // Reset theme to dark
                        htmlElement.setAttribute('data-theme', 'dark');
                        sunIcon.classList.remove('hidden');
                        moonIcon.classList.add('hidden');

                        // Clear localStorage
                        localStorage.removeItem('jj_code_settings');
                        localStorage.removeItem('gemini_api_key');

                        // Remove loading state
                        resetSettingsButton.classList.remove('loading');
                        resetSettingsButton.innerHTML = originalText;

                        // Show success notification
                        showNotification('Settings reset to defaults!', 'info');

                    }, 600);
                }
            });

            // New Prompt Button Functionality
            newPromptButton.addEventListener('click', (event) => {
                createRipple(event);

                // Gate fresh generation until this is pressed
                allowFreshGeneration = true;

                // Add loading state
                newPromptButton.classList.add('loading');
                const originalText = newPromptButton.innerHTML;
                newPromptButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.73 8.26L8.46 10.99C9.5 10.36 10.72 10 12 10C13.28 10 14.5 10.36 15.54 10.99L18.27 8.26C19.57 9.69 20.4 11.84 20.4 14.2H22C22 12.2 21.47 10.34 20.5 8.78L21 9ZM12 15C10.65 15 9.4 14.35 8.65 13.35L5.92 16.08C7.39 18.06 9.58 19.5 12 19.5C14.42 19.5 16.61 18.06 18.08 16.08L15.35 13.35C14.6 14.35 13.35 15 12 15Z"/>Resetting...</svg>';

                setTimeout(() => {
                    // Clear the prompt input
                    promptInput.value = '';
                    promptInput.focus();

                    // Clear the code editor
                    codeEditor.textContent = '// Your generated code will appear here...';
                    updateLivePreview('');
                    currentCode = '';
                    clearInterval(typewriterInterval);

                    // Disable copy button
                    copyButton.disabled = true;
                    // editButton removed
                    selectEditButton.disabled = true;
                    downloadButton.disabled = true;

                    // Remove loading state
                    newPromptButton.classList.remove('loading');
                    newPromptButton.innerHTML = originalText;

                    // Show success notification
                    showNotification('Ready for a new prompt!', 'info');

                    // Add a subtle animation to the prompt input
                    promptInput.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        promptInput.style.transform = 'scale(1)';
                    }, 200);

                }, 300);
            });

            // Download button logic
            function jjGetHtmlToDownload() {
                try {
                    if (typeof currentCode === 'string' && currentCode.trim()) {
                        return currentCode;
                    }
                } catch (_) {}
                return '<!DOCTYPE html>\n' + document.documentElement.outerHTML;
            }

            function jjMinifyHtml(html) {
                return html.replace(/>\s+</g, '><').replace(/\s{2,}/g, ' ').replace(/^\s+|\s+$/g, '');
            }

            function jjDownload(str, filename, mime) {
                const blob = new Blob([str], { type: mime || 'text/html;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                setTimeout(() => { document.body.removeChild(a); URL.revokeObjectURL(url); }, 0);
            }

            if (downloadButton) {
                downloadButton.addEventListener('click', (event) => {
                    createRipple(event);
                    if (!currentCode || currentCode.trim() === '') {
                        showNotification('Nothing to download yet. Generate or edit code first.', 'warning');
                        return;
                    }
                    const html = jjGetHtmlToDownload();
                    jjDownload(html, 'component.html', 'text/html;charset=utf-8');
                    showNotification('Download started (component.html)', 'success');
                });
            }

            // Import Code functionality
            function showImportMessage(message, type = 'error') {
                const existing = document.getElementById('import-message');
                if (existing) existing.remove();

                const msg = document.createElement('div');
                msg.id = 'import-message';
                const bgColor = type === 'success' ? '#dcfce7' : '#fee2e2';
                const textColor = type === 'success' ? '#16a34a' : '#dc2626';
                const borderColor = type === 'success' ? '#bbf7d0' : '#fecaca';

                msg.style.cssText = `position: fixed; top: 80px; right: 20px; background: ${bgColor}; color: ${textColor}; padding: 12px 16px; border-radius: 8px; font-size: 14px; z-index: 10000; border: 1px solid ${borderColor}; white-space: pre-wrap; max-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);`;
                msg.textContent = message;
                document.body.appendChild(msg);

                setTimeout(() => msg.remove(), type === 'success' ? 3000 : 5000);
            }

            function processImportFiles(files) {
                const fileMap = {};
                const requiredFiles = ['index.html', 'styles.css', 'script.js'];

                Array.from(files).forEach(file => {
                    fileMap[file.name] = file;
                });

                const missing = requiredFiles.filter(name => !fileMap[name]);

                if (missing.length > 0) {
                    showImportMessage(`Missing files: ${missing.join(', ')}\nPlease select exactly: index.html, styles.css, script.js`);
                    return;
                }

                Promise.all([
                    fileMap['index.html'].text(),
                    fileMap['styles.css'].text(),
                    fileMap['script.js'].text()
                ]).then(([html, css, js]) => {
                    // Set the imported content to currentCode (assuming HTML is the main content)
                    currentCode = html;

                    // Update the code editor
                    codeEditor.textContent = html;
                    updateLivePreview(html);

                    // Enable buttons
                    copyButton.disabled = false;
                    selectEditButton.disabled = false;
                    downloadButton.disabled = false;

                    showImportMessage('Code imported successfully!', 'success');
                    showNotification('Files imported and loaded into editor', 'success');
                }).catch(err => {
                    showImportMessage('Error reading files: ' + err.message);
                });
            }

            function processImportZip(zipFile) {
                zipFile.arrayBuffer().then(buffer => {
                    const view = new DataView(buffer);
                    const files = {};

                    let offset = 0;
                    while (offset < buffer.byteLength - 4) {
                        const signature = view.getUint32(offset, true);

                        if (signature === 0x04034b50) {
                            const nameLength = view.getUint16(offset + 26, true);
                            const extraLength = view.getUint16(offset + 28, true);
                            const compressedSize = view.getUint32(offset + 18, true);
                            const uncompressedSize = view.getUint32(offset + 22, true);
                            const compressionMethod = view.getUint16(offset + 8, true);

                            if (compressionMethod !== 0) {
                                showImportMessage('ZIP contains compressed files.\nPlease upload the three files directly:\nindex.html, styles.css, script.js');
                                return;
                            }

                            const nameStart = offset + 30;
                            const nameBytes = new Uint8Array(buffer, nameStart, nameLength);
                            const fileName = new TextDecoder().decode(nameBytes);

                            const dataStart = nameStart + nameLength + extraLength;
                            const fileData = new Uint8Array(buffer, dataStart, uncompressedSize);
                            const fileContent = new TextDecoder().decode(fileData);

                            files[fileName] = fileContent;
                            offset = dataStart + compressedSize;
                        } else {
                            offset++;
                        }
                    }

                    const requiredFiles = ['index.html', 'styles.css', 'script.js'];
                    const missing = requiredFiles.filter(name => !files[name]);

                    if (missing.length > 0) {
                        showImportMessage(`ZIP missing files: ${missing.join(', ')}\nZIP must contain exactly:\nindex.html, styles.css, script.js`);
                        return;
                    }

                    // Set the imported content to currentCode
                    currentCode = files['index.html'];

                    // Update the code editor
                    codeEditor.textContent = files['index.html'];
                    updateLivePreview(files['index.html']);

                    // Enable buttons
                    copyButton.disabled = false;
                    selectEditButton.disabled = false;
                    downloadButton.disabled = false;

                    showImportMessage('Code imported from ZIP successfully!', 'success');
                    showNotification('ZIP files imported and loaded into editor', 'success');

                }).catch(err => {
                    showImportMessage('Error reading ZIP file.\nPlease upload the three files directly:\nindex.html, styles.css, script.js');
                });
            }

            function handleImportFiles(files) {
                if (files.length === 1 && files[0].name.endsWith('.zip')) {
                    processImportZip(files[0]);
                } else if (files.length === 3) {
                    processImportFiles(files);
                } else {
                    showImportMessage('Please select either:\n• A ZIP file containing index.html, styles.css, script.js\n• All three files directly');
                }
            }

            if (importButton && importFileInput) {
                processFilesModal.addEventListener('click', async () => {
                    const htmlFileInput = document.getElementById('html-file-modal');
                    const cssFileInput = document.getElementById('css-file-modal');
                    const jsFileInput = document.getElementById('js-file-modal');
                    const combinedFileInput = document.getElementById('combined-file-modal');
                    const zipFileInput = document.getElementById('zip-file-modal');
                    const folderInput = document.getElementById('folder-input-modal');

                    let htmlContent = '', cssContent = '', jsContent = '';

                    // 1. Separate files
                    if (htmlFileInput.files.length > 0 && cssFileInput.files.length > 0 && jsFileInput.files.length > 0) {
                        htmlContent = await htmlFileInput.files[0].text();
                        cssContent = await cssFileInput.files[0].text();
                        jsContent = await jsFileInput.files[0].text();
                    }
                    // 2. Combined file
                    else if (combinedFileInput.files.length > 0) {
                        const combinedHtml = await combinedFileInput.files[0].text();
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(combinedHtml, 'text/html');
                        
                        const styleTag = doc.querySelector('style');
                        if (styleTag) {
                            cssContent = styleTag.innerHTML;
                            styleTag.remove();
                        }

                        const scriptTag = doc.querySelector('script');
                        if (scriptTag) {
                            jsContent = scriptTag.innerHTML;
                            scriptTag.remove();
                        }
                        
                        htmlContent = doc.body.innerHTML;
                    }
                    // 3. Folder input
                    else if (folderInput.files.length > 0) {
                        for (const file of folderInput.files) {
                            if (file.name.endsWith('index.html')) {
                                htmlContent = await file.text();
                            } else if (file.name.endsWith('styles.css')) {
                                cssContent = await file.text();
                            } else if (file.name.endsWith('script.js')) {
                                jsContent = await file.text();
                            }
                        }
                    }
                    // 4. ZIP file
                    else if (zipFileInput.files.length > 0) {
                        const zipFile = zipFileInput.files[0];
                        const zip = await JSZip.loadAsync(zipFile);
                        const htmlFile = zip.file("index.html");
                        const cssFile = zip.file("styles.css");
                        const jsFile = zip.file("script.js");

                        if (htmlFile) htmlContent = await htmlFile.async("string");
                        if (cssFile) cssContent = await cssFile.async("string");
                        if (jsFile) jsContent = await jsFile.async("string");

                        if (!htmlFile && !cssFile && !jsFile) {
                            showNotification("The selected ZIP file does not contain index.html, styles.css, or script.js.", "error");
                        }
                    }

                    // Construct the final HTML
                    currentCode = `<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Imported Code</title>\n    <style>\n${cssContent}\n</style>\n</head>\n<body>\n${htmlContent}\n<` + `script>\n${jsContent}\n</` + `script>\n</body>\n</html>`;
                    
                    codeEditor.textContent = currentCode;
                    updateLivePreview(currentCode);
                    copyButton.disabled = false;
                    selectEditButton.disabled = false;
                    downloadButton.disabled = false;

                    closeImportModal.click();
                    showNotification('Files imported successfully!', 'success');
                });

                importFileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleImportFiles(e.target.files);
                    }
                });

                // Drag and drop support
                importButton.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    importButton.style.background = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)';
                });

                importButton.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    importButton.style.background = 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)';
                });

                importButton.addEventListener('drop', (e) => {
                    e.preventDefault();
                    importButton.style.background = 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)';

                    if (e.dataTransfer.files.length > 0) {
                        handleImportFiles(e.dataTransfer.files);
                    }
                });
            }
        });
    </script>
</body>
</html>
